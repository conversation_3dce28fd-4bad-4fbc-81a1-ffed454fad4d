import { ApiProperty } from '@nestjs/swagger';
import {
  IsNumber,
  IsOptional,
  IsString,
  IsPositive,
  Min,
  Max,
  Length,
} from 'class-validator';

export class CreatePaymentCodeDto {
  @ApiProperty({
    description: '收款金额（可选，不设置则为动态金额）',
    example: 100.00,
    required: false,
  })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  @Max(50000)
  amount?: number;

  @ApiProperty({
    description: '货币类型',
    example: 'CNY',
    default: 'CNY',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(3, 10)
  currency?: string;

  @ApiProperty({
    description: '收款描述',
    example: '商品付款',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  description?: string;

  @ApiProperty({
    description: '过期时间（秒）',
    example: 3600,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(60)
  @Max(86400) // 最长24小时
  expiresIn?: number;

  @ApiProperty({
    description: '最大使用次数',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  maxUsage?: number;
}
