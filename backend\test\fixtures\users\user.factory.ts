import { v4 as uuid } from 'uuid';

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'active',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
  DELETED = 'deleted'
}

/**
 * 用户类型接口
 */
export interface User {
  id: string;
  email: string;
  phone?: string;
  username?: string;
  passwordHash: string;
  firstName?: string;
  lastName?: string;
  status: UserStatus;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 用户数据工厂
 * 用于创建用于测试的用户数据
 */
export class UserFactory {
  /**
   * 创建用户对象
   * @param override 要覆盖的属性
   * @returns 用户对象
   */
  static create(override: Partial<User> = {}): User {
    const now = new Date();

    return {
      id: uuid(),
      email: `test-${Math.floor(Math.random() * 10000)}@example.com`,
      phone: '13800138000',
      username: `testuser${Math.floor(Math.random() * 10000)}`,
      passwordHash: 'hashed_password_for_testing',
      firstName: 'Test',
      lastName: 'User',
      status: UserStatus.ACTIVE,
      avatar: null,
      createdAt: now,
      updatedAt: now,
      ...override
    };
  }

  /**
   * 创建多个用户对象
   * @param count 要创建的用户数量
   * @param override 要覆盖的属性
   * @returns 用户对象数组
   */
  static createMany(count: number, override: Partial<User> = {}): User[] {
    return Array.from({ length: count }, () => this.create(override));
  }

  /**
   * 创建特定状态的用户
   * @param status 用户状态
   * @param override 要覆盖的其他属性
   * @returns 用户对象
   */
  static createWithStatus(status: UserStatus, override: Partial<User> = {}): User {
    return this.create({ status, ...override });
  }
}
