import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import * as CryptoJS from 'crypto-js';

@Injectable()
export class CryptoService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly secretKey: string;

  constructor(private configService: ConfigService) {
    this.secretKey = this.configService.get<string>('ENCRYPTION_KEY') || 
      'your-32-char-encryption-key-here';
  }

  /**
   * 哈希密码
   */
  async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * 验证密码
   */
  async comparePassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * 生成随机盐
   */
  generateSalt(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 加密敏感数据
   */
  encrypt(text: string): string {
    try {
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(this.algorithm, this.secretKey);
      
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
    } catch (error) {
      throw new Error('加密失败');
    }
  }

  /**
   * 解密敏感数据
   */
  decrypt(encryptedData: string): string {
    try {
      const [ivHex, authTagHex, encrypted] = encryptedData.split(':');
      
      const iv = Buffer.from(ivHex, 'hex');
      const authTag = Buffer.from(authTagHex, 'hex');
      
      const decipher = crypto.createDecipher(this.algorithm, this.secretKey);
      decipher.setAuthTag(authTag);
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      throw new Error('解密失败');
    }
  }

  /**
   * 生成随机字符串
   */
  generateRandomString(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * 生成数字验证码
   */
  generateNumericCode(length: number = 6): string {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(Math.random() * (max - min + 1) + min).toString();
  }

  /**
   * 生成UUID
   */
  generateUUID(): string {
    return crypto.randomUUID();
  }

  /**
   * 计算MD5哈希
   */
  md5(text: string): string {
    return crypto.createHash('md5').update(text).digest('hex');
  }

  /**
   * 计算SHA256哈希
   */
  sha256(text: string): string {
    return crypto.createHash('sha256').update(text).digest('hex');
  }

  /**
   * HMAC签名
   */
  hmacSha256(text: string, secret: string): string {
    return crypto.createHmac('sha256', secret).update(text).digest('hex');
  }

  /**
   * 掩码敏感信息（如手机号、银行卡号）
   */
  maskSensitiveData(data: string, type: 'phone' | 'card' | 'email'): string {
    switch (type) {
      case 'phone':
        // 手机号：138****8000
        return data.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
      
      case 'card':
        // 银行卡号：**** **** **** 1234
        return data.replace(/\d{4}(?=\d{4})/g, '****');
      
      case 'email':
        // 邮箱：u***@example.com
        const [username, domain] = data.split('@');
        const maskedUsername = username.charAt(0) + '***';
        return `${maskedUsername}@${domain}`;
      
      default:
        return data;
    }
  }

  /**
   * 生成交易号
   */
  generateTransactionNo(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 999999).toString().padStart(6, '0');
    return `TXN${timestamp}${random}`;
  }

  /**
   * 生成支付码
   */
  generatePaymentCode(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `PAY${timestamp}${random}`.toUpperCase();
  }

  /**
   * 验证数据完整性
   */
  verifyDataIntegrity(data: string, signature: string, secret: string): boolean {
    const expectedSignature = this.hmacSha256(data, secret);
    return signature === expectedSignature;
  }

  /**
   * 生成数据签名
   */
  signData(data: string, secret: string): string {
    return this.hmacSha256(data, secret);
  }

  /**
   * AES加密（用于前端通信）
   */
  aesEncrypt(text: string, key: string): string {
    return CryptoJS.AES.encrypt(text, key).toString();
  }

  /**
   * AES解密（用于前端通信）
   */
  aesDecrypt(encryptedText: string, key: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedText, key);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  /**
   * Base64编码
   */
  base64Encode(text: string): string {
    return Buffer.from(text, 'utf8').toString('base64');
  }

  /**
   * Base64解码
   */
  base64Decode(encodedText: string): string {
    return Buffer.from(encodedText, 'base64').toString('utf8');
  }
}
