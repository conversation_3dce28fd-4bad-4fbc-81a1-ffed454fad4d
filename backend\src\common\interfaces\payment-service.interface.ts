/**
 * 支付方式枚举
 */
export enum PaymentMethod {
  ALIPAY = 'alipay',
  WECHAT = 'wechat',
  BANK_CARD = 'bank_card',
  BALANCE = 'balance'
}

/**
 * 支付结果接口
 */
export interface PaymentResult {
  /**
   * 支付是否成功
   */
  success: boolean;

  /**
   * 交易ID
   */
  transactionId?: string;

  /**
   * 错误信息
   */
  error?: string;

  /**
   * 附加数据
   */
  data?: Record<string, any>;
}

/**
 * 支付服务接口
 * 定义支付相关方法
 */
export interface IPaymentService {
  /**
   * 创建支付订单
   * @param userId 用户ID
   * @param amount 支付金额
   * @param currency 货币类型
   * @param method 支付方式
   * @param metadata 元数据
   * @returns 支付ID和支付链接
   */
  createPayment(
    userId: string,
    amount: number,
    currency: string,
    method: PaymentMethod,
    metadata?: Record<string, any>
  ): Promise<{ paymentId: string; paymentUrl: string }>;

  /**
   * 确认支付
   * @param paymentId 支付ID
   * @returns 支付结果
   */
  confirmPayment(paymentId: string): Promise<PaymentResult>;

  /**
   * 取消支付
   * @param paymentId 支付ID
   */
  cancelPayment(paymentId: string): Promise<void>;

  /**
   * 退款
   * @param transactionId 交易ID
   * @param amount 退款金额
   * @param reason 退款原因
   * @returns 退款结果
   */
  refund(
    transactionId: string,
    amount: number,
    reason?: string
  ): Promise<PaymentResult>;

  /**
   * 获取支付状态
   * @param paymentId 支付ID
   * @returns 支付状态
   */
  getPaymentStatus(paymentId: string): Promise<string>;
}
