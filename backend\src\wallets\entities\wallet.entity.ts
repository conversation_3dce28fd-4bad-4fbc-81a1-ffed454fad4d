import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Transaction } from '../../transactions/entities/transaction.entity';

@Entity('wallets')
@Index(['userId', 'currency'], { unique: true })
export class Wallet {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ length: 10, default: 'CNY' })
  currency: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0.00,
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  balance: number;

  @Column({
    name: 'frozen_balance',
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0.00,
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  frozenBalance: number;

  @Column({
    name: 'total_income',
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0.00,
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalIncome: number;

  @Column({
    name: 'total_expense',
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0.00,
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalExpense: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.wallets, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => Transaction, (transaction) => transaction.fromWallet)
  outgoingTransactions: Transaction[];

  @OneToMany(() => Transaction, (transaction) => transaction.toWallet)
  incomingTransactions: Transaction[];

  // 虚拟属性
  get availableBalance(): number {
    return this.balance - this.frozenBalance;
  }

  get totalBalance(): number {
    return this.balance + this.frozenBalance;
  }

  // 方法
  /**
   * 增加余额
   */
  addBalance(amount: number): void {
    if (amount <= 0) {
      throw new Error('金额必须大于0');
    }
    this.balance += amount;
    this.totalIncome += amount;
  }

  /**
   * 扣减余额
   */
  deductBalance(amount: number): void {
    if (amount <= 0) {
      throw new Error('金额必须大于0');
    }
    if (this.availableBalance < amount) {
      throw new Error('余额不足');
    }
    this.balance -= amount;
    this.totalExpense += amount;
  }

  /**
   * 冻结余额
   */
  freezeBalance(amount: number): void {
    if (amount <= 0) {
      throw new Error('金额必须大于0');
    }
    if (this.availableBalance < amount) {
      throw new Error('可用余额不足');
    }
    this.balance -= amount;
    this.frozenBalance += amount;
  }

  /**
   * 解冻余额
   */
  unfreezeBalance(amount: number): void {
    if (amount <= 0) {
      throw new Error('金额必须大于0');
    }
    if (this.frozenBalance < amount) {
      throw new Error('冻结余额不足');
    }
    this.frozenBalance -= amount;
    this.balance += amount;
  }

  /**
   * 扣减冻结余额
   */
  deductFrozenBalance(amount: number): void {
    if (amount <= 0) {
      throw new Error('金额必须大于0');
    }
    if (this.frozenBalance < amount) {
      throw new Error('冻结余额不足');
    }
    this.frozenBalance -= amount;
    this.totalExpense += amount;
  }

  /**
   * 检查是否有足够的可用余额
   */
  hasAvailableBalance(amount: number): boolean {
    return this.availableBalance >= amount;
  }

  /**
   * 检查是否有足够的冻结余额
   */
  hasFrozenBalance(amount: number): boolean {
    return this.frozenBalance >= amount;
  }

  /**
   * 获取余额信息
   */
  getBalanceInfo(): {
    balance: number;
    frozenBalance: number;
    availableBalance: number;
    totalBalance: number;
    totalIncome: number;
    totalExpense: number;
    currency: string;
  } {
    return {
      balance: this.balance,
      frozenBalance: this.frozenBalance,
      availableBalance: this.availableBalance,
      totalBalance: this.totalBalance,
      totalIncome: this.totalIncome,
      totalExpense: this.totalExpense,
      currency: this.currency,
    };
  }
}
