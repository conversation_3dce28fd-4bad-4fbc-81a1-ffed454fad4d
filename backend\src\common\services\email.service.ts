import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(private configService: ConfigService) {}

  /**
   * 发送邮件验证码
   */
  async sendVerificationCode(email: string, code: string): Promise<void> {
    try {
      const subject = '钱包应用 - 邮箱验证码';
      const html = this.generateVerificationCodeTemplate(code);
      
      await this.sendEmail(email, subject, html);
      this.logger.log(`邮件验证码发送成功: ${email}`);
    } catch (error) {
      this.logger.error(`邮件验证码发送失败: ${email}`, error);
      throw new Error('邮件发送失败');
    }
  }

  /**
   * 发送欢迎邮件
   */
  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    try {
      const subject = '欢迎使用钱包应用';
      const html = this.generateWelcomeTemplate(name);
      
      await this.sendEmail(email, subject, html);
      this.logger.log(`欢迎邮件发送成功: ${email}`);
    } catch (error) {
      this.logger.error(`欢迎邮件发送失败: ${email}`, error);
    }
  }

  /**
   * 发送密码重置邮件
   */
  async sendPasswordResetEmail(email: string, resetToken: string): Promise<void> {
    try {
      const subject = '钱包应用 - 密码重置';
      const html = this.generatePasswordResetTemplate(resetToken);
      
      await this.sendEmail(email, subject, html);
      this.logger.log(`密码重置邮件发送成功: ${email}`);
    } catch (error) {
      this.logger.error(`密码重置邮件发送失败: ${email}`, error);
      throw new Error('邮件发送失败');
    }
  }

  /**
   * 发送交易通知邮件
   */
  async sendTransactionNotification(
    email: string,
    transactionDetails: any,
  ): Promise<void> {
    try {
      const subject = '钱包应用 - 交易通知';
      const html = this.generateTransactionTemplate(transactionDetails);
      
      await this.sendEmail(email, subject, html);
      this.logger.log(`交易通知邮件发送成功: ${email}`);
    } catch (error) {
      this.logger.error(`交易通知邮件发送失败: ${email}`, error);
    }
  }

  /**
   * 发送邮件的核心方法
   */
  private async sendEmail(to: string, subject: string, html: string): Promise<void> {
    const provider = this.configService.get<string>('app.services.email.provider');
    
    switch (provider) {
      case 'smtp':
        await this.sendBySmtp(to, subject, html);
        break;
      case 'sendgrid':
        await this.sendBySendGrid(to, subject, html);
        break;
      case 'mock':
      default:
        await this.sendByMock(to, subject, html);
        break;
    }
  }

  /**
   * SMTP发送邮件
   */
  private async sendBySmtp(to: string, subject: string, html: string): Promise<void> {
    // 这里实现SMTP邮件发送
    // 需要安装 nodemailer 包
    
    this.logger.debug(`SMTP邮件发送: ${to}, 主题: ${subject}`);
    
    // 实际实现示例：
    /*
    const nodemailer = require('nodemailer');
    
    const transporter = nodemailer.createTransporter({
      host: this.configService.get<string>('app.services.email.host'),
      port: this.configService.get<number>('app.services.email.port'),
      secure: this.configService.get<boolean>('app.services.email.secure'),
      auth: {
        user: this.configService.get<string>('app.services.email.user'),
        pass: this.configService.get<string>('app.services.email.password'),
      },
    });

    await transporter.sendMail({
      from: this.configService.get<string>('app.services.email.from'),
      to,
      subject,
      html,
    });
    */
  }

  /**
   * SendGrid发送邮件
   */
  private async sendBySendGrid(to: string, subject: string, html: string): Promise<void> {
    this.logger.debug(`SendGrid邮件发送: ${to}, 主题: ${subject}`);
  }

  /**
   * 模拟邮件发送（开发环境使用）
   */
  private async sendByMock(to: string, subject: string, html: string): Promise<void> {
    this.logger.log(`[模拟邮件] 发送到 ${to}`);
    this.logger.log(`主题: ${subject}`);
    this.logger.debug(`内容: ${html}`);
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  /**
   * 生成验证码邮件模板
   */
  private generateVerificationCodeTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>邮箱验证码</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #007AFF;">钱包应用 - 邮箱验证</h2>
          <p>您好，</p>
          <p>您的邮箱验证码是：</p>
          <div style="background: #f5f5f5; padding: 20px; text-align: center; margin: 20px 0;">
            <span style="font-size: 24px; font-weight: bold; color: #007AFF;">${code}</span>
          </div>
          <p>验证码有效期为5分钟，请及时使用。</p>
          <p>如果这不是您的操作，请忽略此邮件。</p>
          <hr style="margin: 30px 0;">
          <p style="color: #666; font-size: 12px;">
            此邮件由系统自动发送，请勿回复。<br>
            钱包应用团队
          </p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 生成欢迎邮件模板
   */
  private generateWelcomeTemplate(name: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>欢迎使用钱包应用</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #007AFF;">欢迎使用钱包应用！</h2>
          <p>亲爱的 ${name}，</p>
          <p>欢迎加入钱包应用大家庭！您的账户已成功创建。</p>
          <p>钱包应用为您提供：</p>
          <ul>
            <li>安全便捷的转账功能</li>
            <li>多种支付方式</li>
            <li>实时交易记录</li>
            <li>专业的客户服务</li>
          </ul>
          <p>如有任何问题，请随时联系我们的客服团队。</p>
          <hr style="margin: 30px 0;">
          <p style="color: #666; font-size: 12px;">
            钱包应用团队<br>
            客服热线：400-123-4567
          </p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 生成密码重置邮件模板
   */
  private generatePasswordResetTemplate(resetToken: string): string {
    const resetUrl = `${this.configService.get('app.frontendUrl')}/reset-password?token=${resetToken}`;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>密码重置</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #007AFF;">密码重置请求</h2>
          <p>您好，</p>
          <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background: #007AFF; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 5px; display: inline-block;">
              重置密码
            </a>
          </div>
          <p>此链接将在1小时后失效。</p>
          <p>如果这不是您的操作，请忽略此邮件，您的密码不会被更改。</p>
          <hr style="margin: 30px 0;">
          <p style="color: #666; font-size: 12px;">
            此邮件由系统自动发送，请勿回复。<br>
            钱包应用团队
          </p>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 生成交易通知邮件模板
   */
  private generateTransactionTemplate(transaction: any): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>交易通知</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #007AFF;">交易通知</h2>
          <p>您好，</p>
          <p>您的账户发生了一笔交易：</p>
          <div style="background: #f5f5f5; padding: 20px; margin: 20px 0;">
            <p><strong>交易类型：</strong>${transaction.type}</p>
            <p><strong>交易金额：</strong>¥${transaction.amount}</p>
            <p><strong>交易时间：</strong>${transaction.createdAt}</p>
            <p><strong>交易状态：</strong>${transaction.status}</p>
          </div>
          <p>如有疑问，请及时联系客服。</p>
          <hr style="margin: 30px 0;">
          <p style="color: #666; font-size: 12px;">
            钱包应用团队<br>
            客服热线：400-123-4567
          </p>
        </div>
      </body>
      </html>
    `;
  }
}
