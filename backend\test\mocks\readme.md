# 模拟对象

此目录包含测试中使用的模拟(Mock)对象。模拟对象用于替代测试中的外部依赖，如第三方服务、数据库连接等。

## 目录结构

按模块或服务类型组织：

```
/mocks
  /services
    file.service.mock.ts
    email.service.mock.ts
  /repositories
    user.repository.mock.ts
  /providers
    database.provider.mock.ts
```

## 使用指南

### 服务模拟

服务模拟用于替代外部服务依赖：

```typescript
// 使用示例
import { mockFileService } from './mocks/services/file.service.mock';

describe('UsersService', () => {
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        { provide: 'IFileService', useValue: mockFileService }
      ],
    }).compile();
  });
});
```

### 仓库模拟

仓库模拟用于替代数据库访问：

```typescript
// 使用示例
import { MockUserRepository } from './mocks/repositories/user.repository.mock';

describe('UsersService', () => {
  beforeEach(async () => {
    const mockRepo = new MockUserRepository();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        { provide: getRepositoryToken(User), useValue: mockRepo }
      ],
    }).compile();
  });
});
```
