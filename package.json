{"name": "wallet-app-project", "version": "1.0.0", "description": "多平台钱包应用开发项目", "main": "index.js", "scripts": {"setup": "npm run setup:frontend && npm run setup:backend", "setup:frontend": "cd frontend && flutter pub get", "setup:backend": "cd backend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && flutter run", "dev:backend": "cd backend && npm run start:dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && flutter build apk && flutter build ios && flutter build web", "build:backend": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && flutter test", "test:backend": "cd backend && npm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && flutter analyze", "lint:backend": "cd backend && npm run lint", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "deploy:staging": "npm run build && npm run docker:build && kubectl apply -f k8s/staging/", "deploy:production": "npm run build && npm run docker:build && kubectl apply -f k8s/production/"}, "keywords": ["wallet", "payment", "flutter", "fintech", "mobile-app"], "author": "Wallet App Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0", "@commitlint/cli": "^17.6.0", "@commitlint/config-conventional": "^17.6.0", "husky": "^8.0.3", "lint-staged": "^13.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"frontend/**/*.dart": ["cd frontend && flutter format", "cd frontend && flutter analyze"], "backend/**/*.{js,ts}": ["cd backend && npm run lint:fix", "cd backend && npm run format"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}