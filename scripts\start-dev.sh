#!/bin/bash

# 钱包应用开发环境启动脚本

set -e

echo "🚀 启动钱包应用开发环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
    print_message "✓ Docker 正在运行"
}

# 检查端口是否被占用
check_ports() {
    local ports=(3000 5432 6379)
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            print_warning "端口 $port 已被占用"
            read -p "是否继续？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    done
}

# 启动数据库服务
start_databases() {
    print_step "启动数据库服务..."
    
    # 启动 PostgreSQL 和 Redis
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    print_message "等待数据库启动..."
    sleep 10
    
    # 检查数据库连接
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T postgres pg_isready -U wallet_user -d wallet_db > /dev/null 2>&1; then
            print_message "✓ PostgreSQL 已就绪"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "PostgreSQL 启动超时"
            exit 1
        fi
        
        print_message "等待 PostgreSQL 启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    # 检查 Redis 连接
    if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_message "✓ Redis 已就绪"
    else
        print_error "Redis 启动失败"
        exit 1
    fi
}

# 安装后端依赖
install_backend_deps() {
    print_step "安装后端依赖..."
    
    cd backend
    
    if [ ! -d "node_modules" ]; then
        print_message "安装 Node.js 依赖..."
        npm install
    else
        print_message "✓ Node.js 依赖已安装"
    fi
    
    cd ..
}

# 安装前端依赖
install_frontend_deps() {
    print_step "安装前端依赖..."
    
    cd frontend
    
    if [ ! -f ".packages" ]; then
        print_message "安装 Flutter 依赖..."
        flutter pub get
    else
        print_message "✓ Flutter 依赖已安装"
    fi
    
    cd ..
}

# 运行数据库迁移
run_migrations() {
    print_step "运行数据库迁移..."
    
    cd backend
    
    # 等待一下确保数据库完全启动
    sleep 5
    
    # 这里可以添加数据库迁移命令
    # npm run migration:run
    
    print_message "✓ 数据库迁移完成"
    
    cd ..
}

# 启动后端服务
start_backend() {
    print_step "启动后端服务..."
    
    cd backend
    
    # 在后台启动后端服务
    npm run start:dev &
    BACKEND_PID=$!
    
    print_message "后端服务已启动 (PID: $BACKEND_PID)"
    
    # 等待后端服务启动
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/api/v1/health > /dev/null 2>&1; then
            print_message "✓ 后端服务已就绪"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "后端服务启动超时"
            kill $BACKEND_PID 2>/dev/null || true
            exit 1
        fi
        
        print_message "等待后端服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    cd ..
}

# 启动前端应用
start_frontend() {
    print_step "启动前端应用..."
    
    cd frontend
    
    # 检查 Flutter 是否可用
    if ! command -v flutter &> /dev/null; then
        print_warning "Flutter 未安装，跳过前端启动"
        print_warning "请访问 https://flutter.dev/docs/get-started/install 安装 Flutter"
        cd ..
        return
    fi
    
    # 启动 Flutter 应用（Web版本用于开发）
    print_message "启动 Flutter Web 应用..."
    flutter run -d web-server --web-port 8080 &
    FRONTEND_PID=$!
    
    print_message "前端应用已启动 (PID: $FRONTEND_PID)"
    
    cd ..
}

# 显示服务信息
show_services() {
    print_step "服务信息："
    echo ""
    echo "🌐 服务地址："
    echo "   - 后端 API: http://localhost:3000"
    echo "   - API 文档: http://localhost:3000/api/docs"
    echo "   - 前端应用: http://localhost:8080"
    echo ""
    echo "🗄️ 数据库："
    echo "   - PostgreSQL: localhost:5432"
    echo "   - Redis: localhost:6379"
    echo ""
    echo "📊 监控面板："
    echo "   - Grafana: http://localhost:3001 (admin/admin123)"
    echo ""
    echo "🔧 有用的命令："
    echo "   - 查看日志: docker-compose logs -f"
    echo "   - 停止服务: docker-compose down"
    echo "   - 重启数据库: docker-compose restart postgres redis"
    echo ""
    print_message "🎉 开发环境启动完成！"
}

# 清理函数
cleanup() {
    print_step "清理资源..."
    
    # 停止后端服务
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        print_message "后端服务已停止"
    fi
    
    # 停止前端服务
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        print_message "前端服务已停止"
    fi
    
    # 停止数据库服务
    docker-compose down
    print_message "数据库服务已停止"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    echo "========================================"
    echo "    钱包应用开发环境启动脚本"
    echo "========================================"
    echo ""
    
    check_docker
    check_ports
    start_databases
    install_backend_deps
    install_frontend_deps
    run_migrations
    start_backend
    start_frontend
    show_services
    
    # 保持脚本运行
    print_message "按 Ctrl+C 停止所有服务"
    wait
}

# 运行主函数
main "$@"
