import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

export interface EmailJob {
  to: string;
  subject: string;
  template: string;
  data: any;
}

export interface SmsJob {
  phone: string;
  message: string;
  type: 'verification' | 'notification';
}

export interface NotificationJob {
  userId: string;
  title: string;
  content: string;
  type: string;
  data?: any;
}

@Injectable()
export class QueueService {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @InjectQueue('email') private emailQueue: Queue,
    @InjectQueue('sms') private smsQueue: Queue,
    @InjectQueue('notification') private notificationQueue: Queue,
  ) {}

  /**
   * 添加邮件发送任务
   */
  async addEmailJob(
    jobData: EmailJob,
    options?: {
      delay?: number;
      attempts?: number;
      priority?: number;
    },
  ): Promise<void> {
    try {
      await this.emailQueue.add('send-email', jobData, {
        delay: options?.delay || 0,
        attempts: options?.attempts || 3,
        priority: options?.priority || 0,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      });

      this.logger.log(`邮件任务已添加到队列: ${jobData.to}`);
    } catch (error) {
      this.logger.error('添加邮件任务失败', error);
      throw error;
    }
  }

  /**
   * 添加短信发送任务
   */
  async addSmsJob(
    jobData: SmsJob,
    options?: {
      delay?: number;
      attempts?: number;
      priority?: number;
    },
  ): Promise<void> {
    try {
      await this.smsQueue.add('send-sms', jobData, {
        delay: options?.delay || 0,
        attempts: options?.attempts || 3,
        priority: options?.priority || 0,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      });

      this.logger.log(`短信任务已添加到队列: ${jobData.phone}`);
    } catch (error) {
      this.logger.error('添加短信任务失败', error);
      throw error;
    }
  }

  /**
   * 添加通知推送任务
   */
  async addNotificationJob(
    jobData: NotificationJob,
    options?: {
      delay?: number;
      attempts?: number;
      priority?: number;
    },
  ): Promise<void> {
    try {
      await this.notificationQueue.add('send-notification', jobData, {
        delay: options?.delay || 0,
        attempts: options?.attempts || 3,
        priority: options?.priority || 0,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      });

      this.logger.log(`通知任务已添加到队列: ${jobData.userId}`);
    } catch (error) {
      this.logger.error('添加通知任务失败', error);
      throw error;
    }
  }

  /**
   * 批量添加邮件任务
   */
  async addBulkEmailJobs(jobs: EmailJob[]): Promise<void> {
    try {
      const bulkJobs = jobs.map((jobData, index) => ({
        name: 'send-email',
        data: jobData,
        opts: {
          delay: index * 100, // 每个任务间隔100ms
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }));

      await this.emailQueue.addBulk(bulkJobs);
      this.logger.log(`批量邮件任务已添加到队列: ${jobs.length} 个任务`);
    } catch (error) {
      this.logger.error('添加批量邮件任务失败', error);
      throw error;
    }
  }

  /**
   * 批量添加短信任务
   */
  async addBulkSmsJobs(jobs: SmsJob[]): Promise<void> {
    try {
      const bulkJobs = jobs.map((jobData, index) => ({
        name: 'send-sms',
        data: jobData,
        opts: {
          delay: index * 200, // 每个任务间隔200ms
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }));

      await this.smsQueue.addBulk(bulkJobs);
      this.logger.log(`批量短信任务已添加到队列: ${jobs.length} 个任务`);
    } catch (error) {
      this.logger.error('添加批量短信任务失败', error);
      throw error;
    }
  }

  /**
   * 获取队列状态
   */
  async getQueueStats(): Promise<{
    email: any;
    sms: any;
    notification: any;
  }> {
    try {
      const [emailStats, smsStats, notificationStats] = await Promise.all([
        this.getQueueInfo(this.emailQueue),
        this.getQueueInfo(this.smsQueue),
        this.getQueueInfo(this.notificationQueue),
      ]);

      return {
        email: emailStats,
        sms: smsStats,
        notification: notificationStats,
      };
    } catch (error) {
      this.logger.error('获取队列状态失败', error);
      throw error;
    }
  }

  /**
   * 获取单个队列信息
   */
  private async getQueueInfo(queue: Queue): Promise<any> {
    const [waiting, active, completed, failed, delayed] = await Promise.all([
      queue.getWaiting(),
      queue.getActive(),
      queue.getCompleted(),
      queue.getFailed(),
      queue.getDelayed(),
    ]);

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
      delayed: delayed.length,
    };
  }

  /**
   * 清理已完成的任务
   */
  async cleanCompletedJobs(
    queueName: 'email' | 'sms' | 'notification',
    grace: number = 24 * 60 * 60 * 1000, // 24小时
  ): Promise<void> {
    try {
      let queue: Queue;
      
      switch (queueName) {
        case 'email':
          queue = this.emailQueue;
          break;
        case 'sms':
          queue = this.smsQueue;
          break;
        case 'notification':
          queue = this.notificationQueue;
          break;
      }

      await queue.clean(grace, 'completed');
      await queue.clean(grace, 'failed');
      
      this.logger.log(`${queueName} 队列清理完成`);
    } catch (error) {
      this.logger.error(`清理 ${queueName} 队列失败`, error);
      throw error;
    }
  }

  /**
   * 暂停队列
   */
  async pauseQueue(queueName: 'email' | 'sms' | 'notification'): Promise<void> {
    try {
      let queue: Queue;
      
      switch (queueName) {
        case 'email':
          queue = this.emailQueue;
          break;
        case 'sms':
          queue = this.smsQueue;
          break;
        case 'notification':
          queue = this.notificationQueue;
          break;
      }

      await queue.pause();
      this.logger.log(`${queueName} 队列已暂停`);
    } catch (error) {
      this.logger.error(`暂停 ${queueName} 队列失败`, error);
      throw error;
    }
  }

  /**
   * 恢复队列
   */
  async resumeQueue(queueName: 'email' | 'sms' | 'notification'): Promise<void> {
    try {
      let queue: Queue;
      
      switch (queueName) {
        case 'email':
          queue = this.emailQueue;
          break;
        case 'sms':
          queue = this.smsQueue;
          break;
        case 'notification':
          queue = this.notificationQueue;
          break;
      }

      await queue.resume();
      this.logger.log(`${queueName} 队列已恢复`);
    } catch (error) {
      this.logger.error(`恢复 ${queueName} 队列失败`, error);
      throw error;
    }
  }
}
