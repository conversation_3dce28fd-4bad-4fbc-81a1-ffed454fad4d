
import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 存储服务类
/// 提供三种存储方式：
/// 1. Hive: 用于大量、复杂数据存储
/// 2. SharedPreferences: 用于简单键值对存储
/// 3. SecureStorage: 用于敏感数据加密存储
class StorageService {
  // 单例实例
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  // 存储实例
  static late Box _box;
  static late SharedPreferences _prefs;
  static const _secureStorage = FlutterSecureStorage();

  // 盒子名称
  static const String _boxName = 'wallet_app_box';

  // Hive类型ID
  static const int _userTypeId = 1;
  static const int _walletTypeId = 2;
  static const int _transactionTypeId = 3;

  /// 初始化存储
  static Future<void> init() async {
    // 初始化SharedPreferences
    _prefs = await SharedPreferences.getInstance();

    // 打开Hive盒子
    _box = await Hive.openBox(_boxName);

    // 注册适配器 (此处仅占位，实际项目需要实现各自的适配器)
    // Hive.registerAdapter(UserAdapter());
    // Hive.registerAdapter(WalletAdapter());
    // Hive.registerAdapter(TransactionAdapter());

    print('存储服务初始化完成');
  }

  /// 清除所有存储数据
  static Future<void> clearAll() async {
    await _box.clear();
    await _prefs.clear();
    await _secureStorage.deleteAll();
  }

  // ===== Hive存储方法 =====
  // 适用于复杂对象和大量数据

  /// 存储数据到Hive
  static Future<void> saveToBox<T>(String key, T value) async {
    await _box.put(key, value);
  }

  /// 从Hive获取数据
  static T? getFromBox<T>(String key) {
    return _box.get(key) as T?;
  }

  /// 从Hive删除数据
  static Future<void> removeFromBox(String key) async {
    await _box.delete(key);
  }

  // ===== SharedPreferences存储方法 =====
  // 适用于简单键值对

  /// 存储字符串
  static Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  /// 获取字符串
  static String? getString(String key) {
    return _prefs.getString(key);
  }

  /// 存储布尔值
  static Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  /// 获取布尔值
  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  /// 存储整数
  static Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  /// 获取整数
  static int? getInt(String key) {
    return _prefs.getInt(key);
  }

  /// 存储双精度浮点数
  static Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }

  /// 获取双精度浮点数
  static double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  /// 存储字符串列表
  static Future<bool> setStringList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }

  /// 获取字符串列表
  static List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }

  /// 检查键是否存在
  static bool hasKey(String key) {
    return _prefs.containsKey(key);
  }

  /// 移除指定键
  static Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }

  // ===== Secure Storage存储方法 =====
  // 适用于敏感数据，如令牌、密码等

  /// 安全存储字符串
  static Future<void> secureWrite(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  /// 安全读取字符串
  static Future<String?> secureRead(String key) async {
    return await _secureStorage.read(key: key);
  }

  /// 安全删除字符串
  static Future<void> secureDelete(String key) async {
    await _secureStorage.delete(key: key);
  }

  /// 安全存储对象（先序列化为JSON）
  static Future<void> secureWriteObject(String key, Object object) async {
    final jsonString = jsonEncode(object);
    await _secureStorage.write(key: key, value: jsonString);
  }

  /// 安全读取对象（从JSON反序列化）
  static Future<Map<String, dynamic>?> secureReadObject(String key) async {
    final jsonString = await _secureStorage.read(key: key);
    if (jsonString == null) return null;
    return jsonDecode(jsonString) as Map<String, dynamic>;
  }
}
