import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { User, UserStatus, KycStatus } from './entities/user.entity';
import { UserDevice } from './entities/user-device.entity';
import { KycDocument } from './entities/kyc-document.entity';
import { CryptoService } from '../common/services/crypto.service';
import { FileService } from '../common/services/file.service';
import { ValidationService } from '../common/services/validation.service';

import { UpdateProfileDto } from './dto/update-profile.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { UploadKycDto } from './dto/upload-kyc.dto';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserDevice)
    private deviceRepository: Repository<UserDevice>,
    @InjectRepository(KycDocument)
    private kycRepository: Repository<KycDocument>,
    private cryptoService: CryptoService,
    private fileService: FileService,
    private validationService: ValidationService,
  ) {}

  /**
   * 根据ID查找用户
   */
  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['wallets', 'devices'],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return user;
  }

  /**
   * 根据手机号查找用户
   */
  async findByPhone(phone: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { phone },
    });
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email },
    });
  }

  /**
   * 更新用户资料
   */
  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<User> {
    const user = await this.findById(userId);

    // 验证邮箱格式
    if (updateProfileDto.email && !this.validationService.isValidEmail(updateProfileDto.email)) {
      throw new BadRequestException('邮箱格式不正确');
    }

    // 验证真实姓名
    if (updateProfileDto.firstName && !this.validationService.isValidRealName(updateProfileDto.firstName)) {
      throw new BadRequestException('姓名格式不正确');
    }

    if (updateProfileDto.lastName && !this.validationService.isValidRealName(updateProfileDto.lastName)) {
      throw new BadRequestException('姓名格式不正确');
    }

    // 验证年龄
    if (updateProfileDto.dateOfBirth) {
      const birthDate = new Date(updateProfileDto.dateOfBirth);
      if (!this.validationService.isValidAge(birthDate)) {
        throw new BadRequestException('年龄必须在18-100岁之间');
      }
    }

    // 检查邮箱是否已被其他用户使用
    if (updateProfileDto.email && updateProfileDto.email !== user.email) {
      const existingUser = await this.findByEmail(updateProfileDto.email);
      if (existingUser && existingUser.id !== userId) {
        throw new BadRequestException('该邮箱已被其他用户使用');
      }
    }

    // 更新用户信息
    Object.assign(user, updateProfileDto);
    await this.userRepository.save(user);

    this.logger.log(`用户资料更新成功: ${userId}`);
    return user;
  }

  /**
   * 修改密码
   */
  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    const { currentPassword, newPassword } = changePasswordDto;
    const user = await this.findById(userId);

    // 验证当前密码
    const isCurrentPasswordValid = await this.cryptoService.comparePassword(
      currentPassword,
      user.passwordHash,
    );

    if (!isCurrentPasswordValid) {
      throw new BadRequestException('当前密码错误');
    }

    // 验证新密码强度
    if (!this.validationService.isValidPassword(newPassword)) {
      throw new BadRequestException('新密码必须包含字母和数字，长度6-20位');
    }

    // 检查新密码是否与当前密码相同
    const isSamePassword = await this.cryptoService.comparePassword(
      newPassword,
      user.passwordHash,
    );

    if (isSamePassword) {
      throw new BadRequestException('新密码不能与当前密码相同');
    }

    // 更新密码
    user.passwordHash = await this.cryptoService.hashPassword(newPassword);
    user.salt = this.cryptoService.generateSalt();
    await this.userRepository.save(user);

    this.logger.log(`用户密码修改成功: ${userId}`);
  }

  /**
   * 上传头像
   */
  async uploadAvatar(userId: string, file: Express.Multer.File): Promise<string> {
    const user = await this.findById(userId);

    // 验证文件类型
    const allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
    if (!this.fileService.validateFileType(file, allowedTypes)) {
      throw new BadRequestException('只支持 JPG、PNG、GIF 格式的图片');
    }

    // 验证文件大小 (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (!this.fileService.validateFileSize(file, maxSize)) {
      throw new BadRequestException('图片大小不能超过5MB');
    }

    // 删除旧头像
    if (user.avatarUrl) {
      await this.fileService.deleteFile(user.avatarUrl);
    }

    // 上传新头像
    const avatarUrl = await this.fileService.uploadFile(file, 'avatars');

    // 更新用户头像URL
    user.avatarUrl = avatarUrl;
    await this.userRepository.save(user);

    this.logger.log(`用户头像上传成功: ${userId}`);
    return avatarUrl;
  }

  /**
   * 上传KYC文档
   */
  async uploadKycDocument(
    userId: string,
    uploadKycDto: UploadKycDto,
    file: Express.Multer.File,
  ): Promise<KycDocument> {
    const user = await this.findById(userId);

    // 验证文件类型
    const allowedTypes = ['jpg', 'jpeg', 'png', 'pdf'];
    if (!this.fileService.validateFileType(file, allowedTypes)) {
      throw new BadRequestException('只支持 JPG、PNG、PDF 格式的文件');
    }

    // 验证文件大小 (10MB)
    const maxSize = 10 * 1024 * 1024;
    if (!this.fileService.validateFileSize(file, maxSize)) {
      throw new BadRequestException('文件大小不能超过10MB');
    }

    // 上传文件
    const documentUrl = await this.fileService.uploadFile(file, 'kyc');

    // 创建KYC文档记录
    const kycDocument = this.kycRepository.create({
      userId,
      documentType: uploadKycDto.documentType,
      documentNumber: uploadKycDto.documentNumber,
      documentUrl,
    });

    await this.kycRepository.save(kycDocument);

    // 更新用户KYC状态
    user.kycStatus = KycStatus.SUBMITTED;
    await this.userRepository.save(user);

    this.logger.log(`KYC文档上传成功: ${userId}, 类型: ${uploadKycDto.documentType}`);
    return kycDocument;
  }

  /**
   * 获取用户设备列表
   */
  async getUserDevices(userId: string): Promise<UserDevice[]> {
    return this.deviceRepository.find({
      where: { userId },
      order: { lastUsedAt: 'DESC' },
    });
  }

  /**
   * 删除用户设备
   */
  async removeDevice(userId: string, deviceId: string): Promise<void> {
    const device = await this.deviceRepository.findOne({
      where: { userId, deviceId },
    });

    if (!device) {
      throw new NotFoundException('设备不存在');
    }

    await this.deviceRepository.remove(device);
    this.logger.log(`用户设备删除成功: ${userId}, 设备: ${deviceId}`);
  }

  /**
   * 信任设备
   */
  async trustDevice(userId: string, deviceId: string): Promise<void> {
    const device = await this.deviceRepository.findOne({
      where: { userId, deviceId },
    });

    if (!device) {
      throw new NotFoundException('设备不存在');
    }

    device.trust();
    await this.deviceRepository.save(device);
    this.logger.log(`设备已设为信任: ${userId}, 设备: ${deviceId}`);
  }

  /**
   * 取消信任设备
   */
  async untrustDevice(userId: string, deviceId: string): Promise<void> {
    const device = await this.deviceRepository.findOne({
      where: { userId, deviceId },
    });

    if (!device) {
      throw new NotFoundException('设备不存在');
    }

    device.untrust();
    await this.deviceRepository.save(device);
    this.logger.log(`设备已取消信任: ${userId}, 设备: ${deviceId}`);
  }

  /**
   * 获取用户KYC文档
   */
  async getKycDocuments(userId: string): Promise<KycDocument[]> {
    return this.kycRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 停用用户账户
   */
  async deactivateUser(userId: string): Promise<void> {
    const user = await this.findById(userId);
    user.status = UserStatus.INACTIVE;
    await this.userRepository.save(user);
    this.logger.log(`用户账户已停用: ${userId}`);
  }

  /**
   * 激活用户账户
   */
  async activateUser(userId: string): Promise<void> {
    const user = await this.findById(userId);
    user.status = UserStatus.ACTIVE;
    await this.userRepository.save(user);
    this.logger.log(`用户账户已激活: ${userId}`);
  }

  /**
   * 删除用户账户
   */
  async deleteUser(userId: string): Promise<void> {
    const user = await this.findById(userId);
    
    // 软删除：标记为已删除状态
    user.status = UserStatus.DELETED;
    await this.userRepository.save(user);
    
    this.logger.log(`用户账户已删除: ${userId}`);
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats(userId: string): Promise<{
    totalTransactions: number;
    totalAmount: number;
    deviceCount: number;
    kycStatus: KycStatus;
    accountAge: number;
  }> {
    const user = await this.findById(userId);
    const devices = await this.getUserDevices(userId);
    
    // 计算账户年龄（天数）
    const accountAge = Math.floor(
      (Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    );

    return {
      totalTransactions: 0, // 这里需要从交易表查询
      totalAmount: 0, // 这里需要从交易表查询
      deviceCount: devices.length,
      kycStatus: user.kycStatus,
      accountAge,
    };
  }
}
