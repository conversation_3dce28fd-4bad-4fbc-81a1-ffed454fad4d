import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  Length,
} from 'class-validator';

export class UploadKycDto {
  @ApiProperty({
    description: '证件类型',
    example: 'ID_CARD',
    enum: ['ID_CARD', 'PASSPORT', 'DRIVER_LICENSE'],
  })
  @IsString()
  @IsNotEmpty()
  documentType: string;

  @ApiProperty({
    description: '证件号码',
    example: '110101199001011234',
  })
  @IsString()
  @IsNotEmpty()
  @Length(1, 100)
  documentNumber: string;
}
