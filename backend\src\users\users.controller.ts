import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';

import { UsersService } from './users.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from './entities/user.entity';

import { UpdateProfileDto } from './dto/update-profile.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { UploadKycDto } from './dto/upload-kyc.dto';

@ApiTags('users')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('profile')
  @ApiOperation({ summary: '获取用户资料' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getProfile(@CurrentUser() user: User) {
    return this.usersService.findById(user.id);
  }

  @Put('profile')
  @ApiOperation({ summary: '更新用户资料' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateProfile(
    @CurrentUser() user: User,
    @Body() updateProfileDto: UpdateProfileDto,
  ) {
    return this.usersService.updateProfile(user.id, updateProfileDto);
  }

  @Post('change-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '修改密码' })
  @ApiResponse({ status: 200, description: '修改成功' })
  async changePassword(
    @CurrentUser() user: User,
    @Body() changePasswordDto: ChangePasswordDto,
  ) {
    await this.usersService.changePassword(user.id, changePasswordDto);
    return { message: '密码修改成功' };
  }

  @Post('avatar')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '上传头像' })
  @ApiResponse({ status: 201, description: '上传成功' })
  async uploadAvatar(
    @CurrentUser() user: User,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const avatarUrl = await this.usersService.uploadAvatar(user.id, file);
    return { avatarUrl };
  }

  @Post('kyc')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: '上传KYC文档' })
  @ApiResponse({ status: 201, description: '上传成功' })
  async uploadKyc(
    @CurrentUser() user: User,
    @Body() uploadKycDto: UploadKycDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.usersService.uploadKycDocument(user.id, uploadKycDto, file);
  }

  @Get('devices')
  @ApiOperation({ summary: '获取用户设备列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getDevices(@CurrentUser() user: User) {
    return this.usersService.getUserDevices(user.id);
  }

  @Delete('devices/:deviceId')
  @ApiOperation({ summary: '删除用户设备' })
  @ApiResponse({ status: 200, description: '删除成功' })
  async removeDevice(
    @CurrentUser() user: User,
    @Param('deviceId') deviceId: string,
  ) {
    await this.usersService.removeDevice(user.id, deviceId);
    return { message: '设备删除成功' };
  }

  @Get('stats')
  @ApiOperation({ summary: '获取用户统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getStats(@CurrentUser() user: User) {
    return this.usersService.getUserStats(user.id);
  }
}
