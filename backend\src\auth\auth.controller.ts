import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Get,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';

import { AuthService } from './auth.service';
import { Public } from './decorators/public.decorator';
import { CurrentUser } from './decorators/current-user.decorator';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { SendSmsDto, VerifySmsDto } from './dto/verify-sms.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { AuthResponse } from './interfaces/auth-response.interface';
import { User } from '../users/entities/user.entity';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: '用户注册' })
  @ApiResponse({
    status: 201,
    description: '注册成功',
    type: 'object',
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误',
  })
  @ApiResponse({
    status: 409,
    description: '用户已存在',
  })
  @Throttle(5, 60) // 1分钟内最多5次请求
  async register(@Body() registerDto: RegisterDto): Promise<AuthResponse> {
    return this.authService.register(registerDto);
  }

  @Public()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({
    status: 200,
    description: '登录成功',
    type: 'object',
  })
  @ApiResponse({
    status: 401,
    description: '手机号或密码错误',
  })
  @Throttle(10, 60) // 1分钟内最多10次请求
  async login(@Body() loginDto: LoginDto): Promise<AuthResponse> {
    return this.authService.login(loginDto);
  }

  @Public()
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '刷新访问令牌' })
  @ApiResponse({
    status: 200,
    description: '刷新成功',
    type: 'object',
  })
  @ApiResponse({
    status: 401,
    description: '刷新令牌无效',
  })
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
  ): Promise<AuthResponse> {
    return this.authService.refreshToken(refreshTokenDto);
  }

  @Public()
  @Post('sms/send')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '发送短信验证码' })
  @ApiResponse({
    status: 200,
    description: '发送成功',
  })
  @ApiResponse({
    status: 400,
    description: '手机号格式错误或发送频率过高',
  })
  @Throttle(3, 60) // 1分钟内最多3次请求
  async sendSmsCode(@Body() sendSmsDto: SendSmsDto): Promise<void> {
    await this.authService.sendSmsCode(sendSmsDto.phone);
  }

  @Public()
  @Post('sms/verify')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '验证短信验证码' })
  @ApiResponse({
    status: 200,
    description: '验证成功',
  })
  @ApiResponse({
    status: 400,
    description: '验证码错误或已过期',
  })
  @Throttle(10, 60) // 1分钟内最多10次请求
  async verifySmsCode(@Body() verifySmsDto: VerifySmsDto): Promise<{
    valid: boolean;
  }> {
    const valid = await this.authService.verifySmsCode(
      verifySmsDto.phone,
      verifySmsDto.code,
    );
    return { valid };
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: '获取当前用户信息' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: 'object',
  })
  @ApiResponse({
    status: 401,
    description: '未授权',
  })
  async getProfile(@CurrentUser() user: User): Promise<Partial<User>> {
    return {
      id: user.id,
      phone: user.phone,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      avatarUrl: user.avatarUrl,
      isVerified: user.isVerified,
      kycStatus: user.kycStatus,
      createdAt: user.createdAt,
    };
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: '用户登出' })
  @ApiResponse({
    status: 200,
    description: '登出成功',
  })
  async logout(@CurrentUser() user: User): Promise<{ message: string }> {
    // 这里可以实现令牌黑名单机制
    // 目前只是返回成功消息
    return { message: '登出成功' };
  }
}
