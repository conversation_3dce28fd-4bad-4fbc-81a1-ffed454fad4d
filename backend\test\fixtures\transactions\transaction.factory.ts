import { v4 as uuid } from 'uuid';

/**
 * 交易类型枚举
 */
export enum TransactionType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  TRANSFER = 'transfer',
  PAYMENT = 'payment',
  REFUND = 'refund'
}

/**
 * 交易状态枚举
 */
export enum TransactionStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

/**
 * 交易类型接口
 */
export interface Transaction {
  id: string;
  walletId: string;
  toWalletId?: string; // 用于转账交易
  type: TransactionType;
  amount: number;
  fee?: number;
  currency: string;
  status: TransactionStatus;
  reference?: string;
  description?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

/**
 * 交易数据工厂
 * 用于创建用于测试的交易数据
 */
export class TransactionFactory {
  /**
   * 创建交易对象
   * @param override 要覆盖的属性
   * @returns 交易对象
   */
  static create(override: Partial<Transaction> = {}): Transaction {
    const now = new Date();

    return {
      id: uuid(),
      walletId: uuid(),
      type: TransactionType.DEPOSIT,
      amount: 100.00,
      fee: 0,
      currency: 'CNY',
      status: TransactionStatus.COMPLETED,
      reference: `TRX${Math.floor(Math.random() * 1000000)}`,
      description: '测试交易',
      metadata: {},
      createdAt: now,
      updatedAt: now,
      completedAt: now,
      ...override
    };
  }

  /**
   * 创建多个交易对象
   * @param count 要创建的交易数量
   * @param override 要覆盖的属性
   * @returns 交易对象数组
   */
  static createMany(count: number, override: Partial<Transaction> = {}): Transaction[] {
    return Array.from({ length: count }, () => this.create(override));
  }

  /**
   * 创建钱包的交易记录
   * @param walletId 钱包ID
   * @param count 交易数量
   * @returns 交易对象数组
   */
  static createForWallet(walletId: string, count = 1): Transaction[] {
    return this.createMany(count, { walletId });
  }

  /**
   * 创建特定类型的交易
   * @param type 交易类型
   * @param override 要覆盖的其他属性
   * @returns 交易对象
   */
  static createWithType(type: TransactionType, override: Partial<Transaction> = {}): Transaction {
    let description = '测试交易';

    switch (type) {
      case TransactionType.DEPOSIT:
        description = '存款';
        break;
      case TransactionType.WITHDRAWAL:
        description = '取款';
        break;
      case TransactionType.TRANSFER:
        description = '转账';
        break;
      case TransactionType.PAYMENT:
        description = '支付';
        break;
      case TransactionType.REFUND:
        description = '退款';
        break;
    }

    // 转账类型需要目标钱包ID
    const extraData: Partial<Transaction> = {};
    if (type === TransactionType.TRANSFER && !override.toWalletId) {
      extraData.toWalletId = uuid();
    }

    return this.create({ type, description, ...extraData, ...override });
  }

  /**
   * 创建转账交易
   * @param fromWalletId 源钱包ID
   * @param toWalletId 目标钱包ID
   * @param amount 金额
   * @returns 交易对象
   */
  static createTransfer(fromWalletId: string, toWalletId: string, amount = 100): Transaction {
    return this.create({
      walletId: fromWalletId,
      toWalletId,
      type: TransactionType.TRANSFER,
      amount,
      description: '转账',
    });
  }
}
