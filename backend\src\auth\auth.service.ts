import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';

import { User, UserStatus } from '../users/entities/user.entity';
import { UserDevice } from '../users/entities/user-device.entity';
import { CryptoService } from '../common/services/crypto.service';
import { SmsService } from '../common/services/sms.service';
import { ValidationService } from '../common/services/validation.service';

import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { VerifySmsDto } from './dto/verify-sms.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { AuthResponse } from './interfaces/auth-response.interface';
import { JwtPayload } from './interfaces/jwt-payload.interface';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(UserDevice)
    private deviceRepository: Repository<UserDevice>,
    private jwtService: JwtService,
    private configService: ConfigService,
    private cryptoService: CryptoService,
    private smsService: SmsService,
    private validationService: ValidationService,
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,
  ) {}

  /**
   * 用户注册
   */
  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    const { phone, password, smsCode, deviceInfo } = registerDto;

    // 验证手机号格式
    if (!this.validationService.isValidPhone(phone)) {
      throw new BadRequestException('手机号格式不正确');
    }

    // 验证密码强度
    if (!this.validationService.isValidPassword(password)) {
      throw new BadRequestException('密码必须包含字母和数字，长度6-20位');
    }

    // 验证短信验证码
    const isValidSms = await this.verifySmsCode(phone, smsCode);
    if (!isValidSms) {
      throw new BadRequestException('验证码错误或已过期');
    }

    // 检查用户是否已存在
    const existingUser = await this.userRepository.findOne({
      where: { phone },
    });

    if (existingUser) {
      throw new ConflictException('该手机号已注册');
    }

    // 创建用户
    const salt = this.cryptoService.generateSalt();
    const passwordHash = await this.cryptoService.hashPassword(password);

    const user = this.userRepository.create({
      phone,
      passwordHash,
      salt,
      status: UserStatus.ACTIVE,
      isVerified: true, // 通过短信验证即认为已验证
    });

    await this.userRepository.save(user);

    // 记录设备信息
    if (deviceInfo) {
      await this.saveDeviceInfo(user.id, deviceInfo);
    }

    // 生成令牌
    const tokens = await this.generateTokens(user);

    // 清除短信验证码缓存
    await this.clearSmsCode(phone);

    this.logger.log(`用户注册成功: ${phone}`);

    return {
      user: this.sanitizeUser(user),
      ...tokens,
    };
  }

  /**
   * 用户登录
   */
  async login(loginDto: LoginDto): Promise<AuthResponse> {
    const { phone, password, deviceInfo } = loginDto;

    // 查找用户
    const user = await this.userRepository.findOne({
      where: { phone },
    });

    if (!user) {
      throw new UnauthorizedException('手机号或密码错误');
    }

    // 检查账户状态
    if (!user.canLogin()) {
      if (user.isLocked) {
        throw new UnauthorizedException('账户已被锁定，请稍后再试');
      }
      throw new UnauthorizedException('账户已被禁用');
    }

    // 验证密码
    const isPasswordValid = await this.cryptoService.comparePassword(
      password,
      user.passwordHash,
    );

    if (!isPasswordValid) {
      // 增加登录失败次数
      user.incrementLoginAttempts();
      
      // 检查是否需要锁定账户
      const maxAttempts = this.configService.get<number>('app.business.security.maxLoginAttempts');
      if (user.loginAttempts >= maxAttempts) {
        const lockDuration = this.configService.get<number>('app.business.security.lockoutDuration');
        user.lockAccount(lockDuration);
        this.logger.warn(`账户被锁定: ${phone}, 尝试次数: ${user.loginAttempts}`);
      }

      await this.userRepository.save(user);
      throw new UnauthorizedException('手机号或密码错误');
    }

    // 登录成功，重置登录失败次数
    user.resetLoginAttempts();
    user.updateLastLogin();
    await this.userRepository.save(user);

    // 记录设备信息
    if (deviceInfo) {
      await this.saveDeviceInfo(user.id, deviceInfo);
    }

    // 生成令牌
    const tokens = await this.generateTokens(user);

    this.logger.log(`用户登录成功: ${phone}`);

    return {
      user: this.sanitizeUser(user),
      ...tokens,
    };
  }

  /**
   * 刷新令牌
   */
  async refreshToken(refreshTokenDto: RefreshTokenDto): Promise<AuthResponse> {
    const { refreshToken } = refreshTokenDto;

    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('jwt.refreshSecret'),
      });

      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
      });

      if (!user || !user.canLogin()) {
        throw new UnauthorizedException('用户不存在或已被禁用');
      }

      const tokens = await this.generateTokens(user);

      return {
        user: this.sanitizeUser(user),
        ...tokens,
      };
    } catch (error) {
      throw new UnauthorizedException('刷新令牌无效');
    }
  }

  /**
   * 发送短信验证码
   */
  async sendSmsCode(phone: string): Promise<void> {
    // 验证手机号格式
    if (!this.validationService.isValidPhone(phone)) {
      throw new BadRequestException('手机号格式不正确');
    }

    // 检查发送频率限制
    const cacheKey = `sms_rate_limit:${phone}`;
    const sentCount = await this.cacheManager.get<number>(cacheKey) || 0;
    const maxSmsPerDay = this.configService.get<number>('app.business.verification.maxSmsPerDay');

    if (sentCount >= maxSmsPerDay) {
      throw new BadRequestException('今日短信发送次数已达上限');
    }

    // 生成验证码
    const codeLength = this.configService.get<number>('app.business.verification.smsCodeLength');
    const code = this.cryptoService.generateNumericCode(codeLength);

    // 发送短信
    await this.smsService.sendVerificationCode(phone, code);

    // 缓存验证码
    const expiresIn = this.configService.get<number>('app.business.verification.smsCodeExpiresIn');
    await this.cacheManager.set(`sms_code:${phone}`, code, expiresIn * 1000);

    // 更新发送次数
    await this.cacheManager.set(cacheKey, sentCount + 1, 24 * 60 * 60 * 1000); // 24小时

    this.logger.log(`短信验证码已发送: ${phone}`);
  }

  /**
   * 验证短信验证码
   */
  async verifySmsCode(phone: string, code: string): Promise<boolean> {
    const cachedCode = await this.cacheManager.get<string>(`sms_code:${phone}`);
    return cachedCode === code;
  }

  /**
   * 清除短信验证码缓存
   */
  private async clearSmsCode(phone: string): Promise<void> {
    await this.cacheManager.del(`sms_code:${phone}`);
  }

  /**
   * 生成JWT令牌
   */
  private async generateTokens(user: User): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const payload: JwtPayload = {
      sub: user.id,
      phone: user.phone,
      email: user.email,
      isVerified: user.isVerified,
      kycStatus: user.kycStatus,
    };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('jwt.secret'),
        expiresIn: this.configService.get<string>('jwt.expiresIn'),
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('jwt.refreshSecret'),
        expiresIn: this.configService.get<string>('jwt.refreshExpiresIn'),
      }),
    ]);

    return { accessToken, refreshToken };
  }

  /**
   * 保存设备信息
   */
  private async saveDeviceInfo(userId: string, deviceInfo: any): Promise<void> {
    const existingDevice = await this.deviceRepository.findOne({
      where: {
        userId,
        deviceId: deviceInfo.deviceId,
      },
    });

    if (existingDevice) {
      // 更新现有设备信息
      Object.assign(existingDevice, deviceInfo);
      existingDevice.updateLastUsed();
      await this.deviceRepository.save(existingDevice);
    } else {
      // 创建新设备记录
      const device = this.deviceRepository.create({
        userId,
        ...deviceInfo,
      });
      await this.deviceRepository.save(device);
    }
  }

  /**
   * 清理用户敏感信息
   */
  private sanitizeUser(user: User): Partial<User> {
    const { passwordHash, salt, ...sanitizedUser } = user;
    return sanitizedUser;
  }

  /**
   * 验证JWT载荷
   */
  async validateJwtPayload(payload: JwtPayload): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: payload.sub },
    });

    if (!user || !user.canLogin()) {
      throw new UnauthorizedException('用户不存在或已被禁用');
    }

    return user;
  }
}
