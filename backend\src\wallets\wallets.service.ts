import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, EntityManager } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { Wallet } from './entities/wallet.entity';
import { Transaction, TransactionType, TransactionStatus } from '../transactions/entities/transaction.entity';
import { User } from '../users/entities/user.entity';
import { CryptoService } from '../common/services/crypto.service';

@Injectable()
export class WalletsService {
  private readonly logger = new Logger(WalletsService.name);

  constructor(
    @InjectRepository(Wallet)
    private walletRepository: Repository<Wallet>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private dataSource: DataSource,
    private configService: ConfigService,
    private cryptoService: CryptoService,
  ) {}

  /**
   * 创建钱包
   */
  async createWallet(userId: string, currency: string = 'CNY'): Promise<Wallet> {
    // 检查用户是否存在
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 检查是否已存在相同币种的钱包
    const existingWallet = await this.walletRepository.findOne({
      where: { userId, currency },
    });

    if (existingWallet) {
      throw new BadRequestException(`${currency} 钱包已存在`);
    }

    // 创建新钱包
    const wallet = this.walletRepository.create({
      userId,
      currency,
      balance: 0,
      frozenBalance: 0,
      totalIncome: 0,
      totalExpense: 0,
    });

    await this.walletRepository.save(wallet);
    this.logger.log(`钱包创建成功: 用户 ${userId}, 币种 ${currency}`);

    return wallet;
  }

  /**
   * 获取用户钱包
   */
  async getUserWallet(userId: string, currency: string = 'CNY'): Promise<Wallet> {
    const wallet = await this.walletRepository.findOne({
      where: { userId, currency },
      relations: ['user'],
    });

    if (!wallet) {
      // 自动创建钱包
      return this.createWallet(userId, currency);
    }

    return wallet;
  }

  /**
   * 获取用户所有钱包
   */
  async getUserWallets(userId: string): Promise<Wallet[]> {
    return this.walletRepository.find({
      where: { userId },
      order: { currency: 'ASC' },
    });
  }

  /**
   * 转账
   */
  async transfer(
    fromUserId: string,
    toUserId: string,
    amount: number,
    currency: string = 'CNY',
    description?: string,
  ): Promise<Transaction> {
    // 验证转账金额
    const minAmount = this.configService.get<number>('app.business.transfer.minAmount');
    const maxAmount = this.configService.get<number>('app.business.transfer.maxAmount');

    if (amount < minAmount || amount > maxAmount) {
      throw new BadRequestException(`转账金额必须在 ${minAmount} - ${maxAmount} 之间`);
    }

    // 检查是否自己转给自己
    if (fromUserId === toUserId) {
      throw new BadRequestException('不能转账给自己');
    }

    // 检查用户是否存在
    const [fromUser, toUser] = await Promise.all([
      this.userRepository.findOne({ where: { id: fromUserId } }),
      this.userRepository.findOne({ where: { id: toUserId } }),
    ]);

    if (!fromUser) {
      throw new NotFoundException('转出用户不存在');
    }

    if (!toUser) {
      throw new NotFoundException('转入用户不存在');
    }

    // 获取钱包
    const [fromWallet, toWallet] = await Promise.all([
      this.getUserWallet(fromUserId, currency),
      this.getUserWallet(toUserId, currency),
    ]);

    // 计算手续费
    const feeRate = this.configService.get<number>('app.business.transfer.feeRate');
    const fee = amount * feeRate;
    const totalAmount = amount + fee;

    // 检查余额
    if (!fromWallet.hasAvailableBalance(totalAmount)) {
      throw new BadRequestException('余额不足');
    }

    // 使用数据库事务
    return this.dataSource.transaction(async (manager: EntityManager) => {
      // 创建交易记录
      const transaction = manager.create(Transaction, {
        transactionNo: this.cryptoService.generateTransactionNo(),
        fromUserId,
        toUserId,
        fromWalletId: fromWallet.id,
        toWalletId: toWallet.id,
        amount,
        fee,
        currency,
        type: TransactionType.TRANSFER,
        status: TransactionStatus.PENDING,
        description,
      });

      await manager.save(transaction);

      try {
        // 开始处理交易
        transaction.startProcessing();
        await manager.save(transaction);

        // 扣减转出方余额
        fromWallet.deductBalance(totalAmount);
        await manager.save(fromWallet);

        // 增加转入方余额
        toWallet.addBalance(amount);
        await manager.save(toWallet);

        // 完成交易
        transaction.complete();
        await manager.save(transaction);

        this.logger.log(
          `转账成功: ${fromUserId} -> ${toUserId}, 金额: ${amount} ${currency}, 交易号: ${transaction.transactionNo}`,
        );

        return transaction;
      } catch (error) {
        // 交易失败
        transaction.fail(error.message);
        await manager.save(transaction);
        throw error;
      }
    });
  }

  /**
   * 充值
   */
  async deposit(
    userId: string,
    amount: number,
    currency: string = 'CNY',
    paymentMethod: string,
    referenceId?: string,
  ): Promise<Transaction> {
    if (amount <= 0) {
      throw new BadRequestException('充值金额必须大于0');
    }

    const wallet = await this.getUserWallet(userId, currency);

    return this.dataSource.transaction(async (manager: EntityManager) => {
      // 创建交易记录
      const transaction = manager.create(Transaction, {
        transactionNo: this.cryptoService.generateTransactionNo(),
        toUserId: userId,
        toWalletId: wallet.id,
        amount,
        fee: 0,
        currency,
        type: TransactionType.DEPOSIT,
        status: TransactionStatus.PROCESSING,
        paymentMethod,
        referenceId,
      });

      await manager.save(transaction);

      // 增加余额
      wallet.addBalance(amount);
      await manager.save(wallet);

      // 完成交易
      transaction.complete();
      await manager.save(transaction);

      this.logger.log(
        `充值成功: 用户 ${userId}, 金额: ${amount} ${currency}, 交易号: ${transaction.transactionNo}`,
      );

      return transaction;
    });
  }

  /**
   * 提现
   */
  async withdraw(
    userId: string,
    amount: number,
    currency: string = 'CNY',
    paymentMethod: string,
    referenceId?: string,
  ): Promise<Transaction> {
    if (amount <= 0) {
      throw new BadRequestException('提现金额必须大于0');
    }

    const wallet = await this.getUserWallet(userId, currency);

    // 检查余额
    if (!wallet.hasAvailableBalance(amount)) {
      throw new BadRequestException('余额不足');
    }

    return this.dataSource.transaction(async (manager: EntityManager) => {
      // 创建交易记录
      const transaction = manager.create(Transaction, {
        transactionNo: this.cryptoService.generateTransactionNo(),
        fromUserId: userId,
        fromWalletId: wallet.id,
        amount,
        fee: 0,
        currency,
        type: TransactionType.WITHDRAWAL,
        status: TransactionStatus.PROCESSING,
        paymentMethod,
        referenceId,
      });

      await manager.save(transaction);

      // 扣减余额
      wallet.deductBalance(amount);
      await manager.save(wallet);

      // 完成交易
      transaction.complete();
      await manager.save(transaction);

      this.logger.log(
        `提现成功: 用户 ${userId}, 金额: ${amount} ${currency}, 交易号: ${transaction.transactionNo}`,
      );

      return transaction;
    });
  }

  /**
   * 冻结余额
   */
  async freezeBalance(
    userId: string,
    amount: number,
    currency: string = 'CNY',
    reason?: string,
  ): Promise<void> {
    const wallet = await this.getUserWallet(userId, currency);

    if (!wallet.hasAvailableBalance(amount)) {
      throw new BadRequestException('可用余额不足');
    }

    wallet.freezeBalance(amount);
    await this.walletRepository.save(wallet);

    this.logger.log(
      `余额冻结成功: 用户 ${userId}, 金额: ${amount} ${currency}, 原因: ${reason}`,
    );
  }

  /**
   * 解冻余额
   */
  async unfreezeBalance(
    userId: string,
    amount: number,
    currency: string = 'CNY',
    reason?: string,
  ): Promise<void> {
    const wallet = await this.getUserWallet(userId, currency);

    if (!wallet.hasFrozenBalance(amount)) {
      throw new BadRequestException('冻结余额不足');
    }

    wallet.unfreezeBalance(amount);
    await this.walletRepository.save(wallet);

    this.logger.log(
      `余额解冻成功: 用户 ${userId}, 金额: ${amount} ${currency}, 原因: ${reason}`,
    );
  }

  /**
   * 获取钱包余额
   */
  async getBalance(userId: string, currency: string = 'CNY'): Promise<{
    balance: number;
    frozenBalance: number;
    availableBalance: number;
    totalBalance: number;
    currency: string;
  }> {
    const wallet = await this.getUserWallet(userId, currency);
    
    return {
      balance: wallet.balance,
      frozenBalance: wallet.frozenBalance,
      availableBalance: wallet.availableBalance,
      totalBalance: wallet.totalBalance,
      currency: wallet.currency,
    };
  }

  /**
   * 获取钱包统计信息
   */
  async getWalletStats(userId: string, currency: string = 'CNY'): Promise<{
    balance: number;
    frozenBalance: number;
    availableBalance: number;
    totalIncome: number;
    totalExpense: number;
    transactionCount: number;
    currency: string;
  }> {
    const wallet = await this.getUserWallet(userId, currency);
    
    // 统计交易次数
    const transactionCount = await this.transactionRepository.count({
      where: [
        { fromUserId: userId, currency },
        { toUserId: userId, currency },
      ],
    });

    return {
      balance: wallet.balance,
      frozenBalance: wallet.frozenBalance,
      availableBalance: wallet.availableBalance,
      totalIncome: wallet.totalIncome,
      totalExpense: wallet.totalExpense,
      transactionCount,
      currency: wallet.currency,
    };
  }
}
