import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';

@Entity('user_devices')
@Index(['userId', 'deviceId'], { unique: true })
export class UserDevice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'device_id' })
  deviceId: string;

  @Column({ name: 'device_name', nullable: true })
  deviceName: string;

  @Column({ name: 'device_type', nullable: true, length: 50 })
  deviceType: string;

  @Column({ name: 'os_version', nullable: true, length: 50 })
  osVersion: string;

  @Column({ name: 'app_version', nullable: true, length: 50 })
  appVersion: string;

  @Column({ name: 'push_token', nullable: true })
  pushToken: string;

  @Column({ name: 'is_trusted', default: false })
  isTrusted: boolean;

  @Column({ name: 'last_used_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  lastUsedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.devices, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // 方法
  updateLastUsed(): void {
    this.lastUsedAt = new Date();
  }

  trust(): void {
    this.isTrusted = true;
  }

  untrust(): void {
    this.isTrusted = false;
  }
}
