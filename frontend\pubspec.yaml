name: wallet_app
description: 多平台钱包应用 - 类似支付宝/微信支付的现代化钱包应用

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI组件
  cupertino_icons: ^1.0.6
  flutter_svg: ^2.0.7
  cached_network_image: ^3.2.3
  shimmer: ^3.0.0
  lottie: ^2.6.0
  
  # 状态管理
  flutter_riverpod: ^2.4.0
  riverpod_annotation: ^2.1.1
  
  # 路由导航
  go_router: ^10.1.2
  
  # 网络请求
  dio: ^5.3.2
  retrofit: ^4.0.1
  json_annotation: ^4.8.1
  
  # 本地存储
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.0
  flutter_secure_storage: ^9.0.0
  
  # 工具类
  intl: ^0.18.1
  uuid: ^3.0.7
  crypto: ^3.0.3
  convert: ^3.1.1
  
  # 设备功能
  local_auth: ^2.1.6
  device_info_plus: ^9.1.0
  package_info_plus: ^4.1.0
  permission_handler: ^10.4.3
  
  # 相机和图片
  image_picker: ^1.0.4
  camera: ^0.10.5+2
  qr_code_scanner: ^1.0.1
  qr_flutter: ^4.1.0
  
  # 地图和位置
  geolocator: ^9.0.2
  geocoding: ^2.1.0
  
  # 推送通知
  firebase_core: ^2.15.1
  firebase_messaging: ^14.6.7
  flutter_local_notifications: ^15.1.1
  
  # 动画
  animations: ^2.0.8
  rive: ^0.11.4
  
  # 其他
  url_launcher: ^6.1.12
  share_plus: ^7.1.0
  connectivity_plus: ^4.0.2
  flutter_launcher_icons: ^0.13.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # 代码生成
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  retrofit_generator: ^7.0.8
  riverpod_generator: ^2.2.3
  hive_generator: ^2.0.0
  
  # 代码质量
  flutter_lints: ^2.0.3
  very_good_analysis: ^5.1.0
  
  # 测试
  mockito: ^5.4.2
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
  
  fonts:
    - family: SF Pro Display
      fonts:
        - asset: assets/fonts/SFProDisplay-Regular.ttf
        - asset: assets/fonts/SFProDisplay-Medium.ttf
          weight: 500
        - asset: assets/fonts/SFProDisplay-Semibold.ttf
          weight: 600
        - asset: assets/fonts/SFProDisplay-Bold.ttf
          weight: 700
    
    - family: SF Pro Text
      fonts:
        - asset: assets/fonts/SFProText-Regular.ttf
        - asset: assets/fonts/SFProText-Medium.ttf
          weight: 500
        - asset: assets/fonts/SFProText-Semibold.ttf
          weight: 600

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icons/app_icon.png"
