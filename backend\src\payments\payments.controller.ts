import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Delete,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { PaymentsService } from './payments.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

import { CreatePaymentCodeDto } from './dto/create-payment-code.dto';
import { CreateRedPacketDto } from './dto/create-red-packet.dto';
import { PayByCodeDto } from './dto/pay-by-code.dto';

@ApiTags('payments')
@Controller('payments')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post('codes')
  @ApiOperation({ summary: '创建收款码' })
  @ApiResponse({ status: 201, description: '创建成功' })
  async createPaymentCode(
    @CurrentUser() user: User,
    @Body() createPaymentCodeDto: CreatePaymentCodeDto,
  ) {
    return this.paymentsService.createPaymentCode(user.id, createPaymentCodeDto);
  }

  @Get('codes')
  @ApiOperation({ summary: '获取用户收款码列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserPaymentCodes(@CurrentUser() user: User) {
    return this.paymentsService.getUserPaymentCodes(user.id);
  }

  @Post('pay')
  @ApiOperation({ summary: '通过收款码支付' })
  @ApiResponse({ status: 201, description: '支付成功' })
  async payByCode(
    @CurrentUser() user: User,
    @Body() payByCodeDto: PayByCodeDto,
  ) {
    return this.paymentsService.payByCode(user.id, payByCodeDto);
  }

  @Post('red-packets')
  @ApiOperation({ summary: '创建红包' })
  @ApiResponse({ status: 201, description: '创建成功' })
  async createRedPacket(
    @CurrentUser() user: User,
    @Body() createRedPacketDto: CreateRedPacketDto,
  ) {
    return this.paymentsService.createRedPacket(user.id, createRedPacketDto);
  }

  @Post('red-packets/:id/claim')
  @ApiOperation({ summary: '领取红包' })
  @ApiResponse({ status: 201, description: '领取成功' })
  async claimRedPacket(
    @CurrentUser() user: User,
    @Param('id') redPacketId: string,
  ) {
    return this.paymentsService.claimRedPacket(user.id, redPacketId);
  }

  @Get('red-packets/sent')
  @ApiOperation({ summary: '获取发送的红包列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSentRedPackets(@CurrentUser() user: User) {
    return this.paymentsService.getUserSentRedPackets(user.id);
  }

  @Get('red-packets/claimed')
  @ApiOperation({ summary: '获取领取的红包列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getClaimedRedPackets(@CurrentUser() user: User) {
    return this.paymentsService.getUserClaimedRedPackets(user.id);
  }
}
