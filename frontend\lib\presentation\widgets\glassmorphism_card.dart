import 'dart:ui';
import 'package:flutter/material.dart';

/// 液态毛玻璃效果卡片组件
/// 提供现代化的iOS风格毛玻璃背景效果
class GlassmorphismCard extends StatelessWidget {
  /// 子组件
  final Widget child;
  
  /// 模糊强度 (0-100)
  final double blur;
  
  /// 透明度 (0.0-1.0)
  final double opacity;
  
  /// 边框圆角
  final BorderRadius borderRadius;
  
  /// 边框颜色
  final Color borderColor;
  
  /// 边框宽度
  final double borderWidth;
  
  /// 背景渐变色
  final Gradient? gradient;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 内边距
  final EdgeInsetsGeometry? padding;
  
  /// 外边距
  final EdgeInsetsGeometry? margin;
  
  /// 宽度
  final double? width;
  
  /// 高度
  final double? height;
  
  /// 阴影
  final List<BoxShadow>? boxShadow;
  
  /// 点击事件
  final VoidCallback? onTap;
  
  /// 长按事件
  final VoidCallback? onLongPress;

  const GlassmorphismCard({
    super.key,
    required this.child,
    this.blur = 10.0,
    this.opacity = 0.2,
    this.borderRadius = const BorderRadius.all(Radius.circular(20)),
    this.borderColor = Colors.white,
    this.borderWidth = 1.0,
    this.gradient,
    this.backgroundColor,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.boxShadow,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    // 默认背景色
    final defaultBackgroundColor = isDark 
        ? Colors.white.withOpacity(0.1)
        : Colors.white.withOpacity(opacity);
    
    // 默认边框色
    final defaultBorderColor = isDark
        ? Colors.white.withOpacity(0.2)
        : borderColor.withOpacity(0.3);
    
    // 默认阴影
    final defaultShadow = isDark
        ? [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ]
        : [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ];

    Widget content = Container(
      width: width,
      height: height,
      margin: margin,
      child: ClipRRect(
        borderRadius: borderRadius,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
          child: Container(
            padding: padding,
            decoration: BoxDecoration(
              gradient: gradient,
              color: gradient == null 
                  ? (backgroundColor ?? defaultBackgroundColor)
                  : null,
              borderRadius: borderRadius,
              border: Border.all(
                color: defaultBorderColor,
                width: borderWidth,
              ),
              boxShadow: boxShadow ?? defaultShadow,
            ),
            child: child,
          ),
        ),
      ),
    );

    // 如果有点击事件，包装在GestureDetector中
    if (onTap != null || onLongPress != null) {
      content = GestureDetector(
        onTap: onTap,
        onLongPress: onLongPress,
        child: content,
      );
    }

    return content;
  }
}

/// 毛玻璃效果按钮
class GlassmorphismButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final double blur;
  final double opacity;
  final BorderRadius borderRadius;
  final Color? backgroundColor;
  final Gradient? gradient;
  final EdgeInsetsGeometry padding;
  final double? width;
  final double? height;
  final bool enabled;

  const GlassmorphismButton({
    super.key,
    required this.child,
    this.onPressed,
    this.blur = 10.0,
    this.opacity = 0.2,
    this.borderRadius = const BorderRadius.all(Radius.circular(16)),
    this.backgroundColor,
    this.gradient,
    this.padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    this.width,
    this.height,
    this.enabled = true,
  });

  @override
  State<GlassmorphismButton> createState() => _GlassmorphismButtonState();
}

class _GlassmorphismButtonState extends State<GlassmorphismButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.enabled && widget.onPressed != null) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
      widget.onPressed?.call();
    }
  }

  void _handleTapCancel() {
    if (_isPressed) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: GlassmorphismCard(
              blur: widget.blur,
              opacity: widget.enabled ? widget.opacity : widget.opacity * 0.5,
              borderRadius: widget.borderRadius,
              backgroundColor: widget.backgroundColor,
              gradient: widget.gradient,
              padding: widget.padding,
              width: widget.width,
              height: widget.height,
              child: AnimatedOpacity(
                duration: const Duration(milliseconds: 200),
                opacity: widget.enabled ? 1.0 : 0.5,
                child: widget.child,
              ),
            ),
          );
        },
      ),
    );
  }
}

/// 毛玻璃效果底部弹窗
class GlassmorphismBottomSheet extends StatelessWidget {
  final Widget child;
  final double blur;
  final double opacity;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final double? height;
  final EdgeInsetsGeometry? padding;

  const GlassmorphismBottomSheet({
    super.key,
    required this.child,
    this.blur = 20.0,
    this.opacity = 0.9,
    this.borderRadius,
    this.backgroundColor,
    this.height,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final defaultBorderRadius = borderRadius ?? 
        const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        );

    return GlassmorphismCard(
      blur: blur,
      opacity: opacity,
      borderRadius: defaultBorderRadius,
      backgroundColor: backgroundColor,
      padding: padding ?? const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          child,
        ],
      ),
    );
  }
}

/// 毛玻璃效果应用栏
class GlassmorphismAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final Widget? leading;
  final List<Widget>? actions;
  final double blur;
  final double opacity;
  final Color? backgroundColor;
  final double elevation;

  const GlassmorphismAppBar({
    super.key,
    this.title,
    this.leading,
    this.actions,
    this.blur = 20.0,
    this.opacity = 0.8,
    this.backgroundColor,
    this.elevation = 0,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: Container(
          decoration: BoxDecoration(
            color: backgroundColor ?? 
                Theme.of(context).scaffoldBackgroundColor.withOpacity(opacity),
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).dividerColor.withOpacity(0.3),
                width: 0.5,
              ),
            ),
          ),
          child: AppBar(
            title: title,
            leading: leading,
            actions: actions,
            backgroundColor: Colors.transparent,
            elevation: elevation,
            scrolledUnderElevation: 0,
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
