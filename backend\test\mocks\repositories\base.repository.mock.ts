/**
 * 基础模拟仓库类
 * 为所有实体提供通用的仓库操作
 */
export class BaseMockRepository<T> {
  protected entities: T[] = [];

  /**
   * 获取实体主键的字段名称
   * 默认为'id'，可在子类中覆盖
   */
  protected getPrimaryKeyField(): string {
    return 'id';
  }

  /**
   * 清空仓库
   */
  clear(): void {
    this.entities = [];
  }

  /**
   * 初始化仓库数据
   * @param entities 初始实体数组
   */
  initialize(entities: T[]): void {
    this.entities = [...entities];
  }

  /**
   * 查找所有实体
   * @returns 所有实体数组
   */
  find(): Promise<T[]> {
    return Promise.resolve([...this.entities]);
  }

  /**
   * 根据条件查找实体
   * @param conditions 查询条件
   * @returns 匹配的实体数组
   */
  findBy(conditions: Partial<T>): Promise<T[]> {
    return Promise.resolve(
      this.entities.filter(entity => {
        return Object.entries(conditions).every(([key, value]) => {
          return entity[key] === value;
        });
      })
    );
  }

  /**
   * 查找单个实体
   * @param conditions 查询条件
   * @returns 匹配的实体或undefined
   */
  findOne(conditions: Partial<T>): Promise<T | null> {
    const result = this.entities.find(entity => {
      return Object.entries(conditions).every(([key, value]) => {
        return entity[key] === value;
      });
    });

    return Promise.resolve(result || null);
  }

  /**
   * 根据ID查找实体
   * @param id 实体ID
   * @returns 匹配的实体或undefined
   */
  findOneBy(conditions: Partial<T>): Promise<T | null> {
    return this.findOne(conditions);
  }

  /**
   * 根据ID查找实体
   * @param id 实体ID
   * @returns 匹配的实体或undefined
   */
  findOneById(id: any): Promise<T | null> {
    const keyField = this.getPrimaryKeyField();
    return this.findOne({ [keyField]: id } as any);
  }

  /**
   * 保存实体
   * @param entity 要保存的实体
   * @returns 保存后的实体
   */
  save(entity: Partial<T>): Promise<T> {
    const keyField = this.getPrimaryKeyField();
    const id = entity[keyField];

    if (id) {
      // 更新现有实体
      const index = this.entities.findIndex(e => e[keyField] === id);

      if (index >= 0) {
        this.entities[index] = { ...this.entities[index], ...entity };
        return Promise.resolve(this.entities[index] as T);
      }
    }

    // 创建新实体
    const newEntity = { ...entity } as T;
    this.entities.push(newEntity);

    return Promise.resolve(newEntity);
  }

  /**
   * 保存多个实体
   * @param entities 要保存的实体数组
   * @returns 保存后的实体数组
   */
  saveMany(entities: Partial<T>[]): Promise<T[]> {
    return Promise.all(entities.map(entity => this.save(entity)));
  }

  /**
   * 删除实体
   * @param entity 要删除的实体或条件
   * @returns 删除结果
   */
  remove(entity: Partial<T>): Promise<void> {
    const keyField = this.getPrimaryKeyField();
    const id = entity[keyField];

    if (id) {
      this.entities = this.entities.filter(e => e[keyField] !== id);
    }

    return Promise.resolve();
  }

  /**
   * 根据条件删除实体
   * @param conditions 删除条件
   * @returns 删除结果
   */
  delete(conditions: Partial<T>): Promise<void> {
    this.entities = this.entities.filter(entity => {
      return !Object.entries(conditions).every(([key, value]) => {
        return entity[key] === value;
      });
    });

    return Promise.resolve();
  }

  /**
   * 统计符合条件的实体数量
   * @param conditions 统计条件
   * @returns 实体数量
   */
  count(conditions: Partial<T> = {}): Promise<number> {
    const filteredEntities = this.entities.filter(entity => {
      return Object.entries(conditions).every(([key, value]) => {
        return entity[key] === value;
      });
    });

    return Promise.resolve(filteredEntities.length);
  }

  /**
   * 清空表数据
   */
  async clear(): Promise<void> {
    this.entities = [];
    return Promise.resolve();
  }

  /**
   * 执行自定义查询
   * 此方法仅用于模拟，不会真正执行SQL
   */
  query(query: string, parameters?: any[]): Promise<any[]> {
    // 模拟查询操作，实际测试中不会真正执行SQL
    console.log(`[MOCK] Executing query: ${query}`);
    return Promise.resolve([]);
  }
}
