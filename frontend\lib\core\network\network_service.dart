
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../constants/app_constants.dart';
import '../storage/storage_service.dart';

/// 网络服务类
/// 负责处理与后端API的通信
class NetworkService {
  // 单例实例
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  // Dio实例
  static late Dio _dio;

  /// 初始化网络服务
  static void init() {
    // 创建Dio基础配置
    final baseOptions = BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: AppConstants.connectTimeout,
      receiveTimeout: AppConstants.receiveTimeout,
      sendTimeout: AppConstants.sendTimeout,
      responseType: ResponseType.json,
      contentType: Headers.jsonContentType,
      validateStatus: (status) => status != null && status < 500,
    );

    // 创建Dio实例
    _dio = Dio(baseOptions);

    // 添加拦截器
    _setupInterceptors();

    print('网络服务初始化完成');
  }

  /// 设置Dio拦截器
  static void _setupInterceptors() {
    // 日志拦截器
    if (AppConstants.isDebug && AppConstants.enableLogging) {
      _dio.interceptors.add(LogInterceptor(
        request: true,
        requestHeader: true,
        requestBody: true,
        responseHeader: true,
        responseBody: true,
        error: true,
      ));
    }

    // 认证拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 从安全存储获取访问令牌
        final accessToken = await StorageService.secureRead(AppConstants.accessTokenKey);
        if (accessToken != null) {
          options.headers['Authorization'] = 'Bearer $accessToken';
        }
        return handler.next(options);
      },
      onError: (DioException error, handler) async {
        // 处理401错误(未授权) - 尝试刷新令牌
        if (error.response?.statusCode == 401) {
          try {
            final refreshed = await _refreshToken();
            if (refreshed) {
              // 令牌刷新成功，重试原始请求
              final accessToken = await StorageService.secureRead(AppConstants.accessTokenKey);
              error.requestOptions.headers['Authorization'] = 'Bearer $accessToken';

              // 创建新请求并发送
              final response = await _dio.request(
                error.requestOptions.path,
                data: error.requestOptions.data,
                queryParameters: error.requestOptions.queryParameters,
                options: Options(
                  method: error.requestOptions.method,
                  headers: error.requestOptions.headers,
                ),
              );

              return handler.resolve(response);
            }
          } catch (e) {
            print('令牌刷新失败: $e');
            // 处理令牌刷新失败 - 通常需要重新登录
            // TODO: 实现重定向到登录页面的逻辑
          }
        }

        return handler.next(error);
      },
    ));
  }

  /// 刷新访问令牌
  static Future<bool> _refreshToken() async {
    try {
      final refreshToken = await StorageService.secureRead(AppConstants.refreshTokenKey);
      if (refreshToken == null) return false;

      // 创建一个新的Dio实例，避免无限循环拦截
      final tokenDio = Dio(BaseOptions(baseUrl: AppConstants.baseUrl));

      final response = await tokenDio.post(
        '/auth/refresh',
        data: {'refreshToken': refreshToken},
      );

      if (response.statusCode == 200 && response.data != null) {
        // 保存新的访问令牌和刷新令牌
        await StorageService.secureWrite(
          AppConstants.accessTokenKey, 
          response.data['accessToken']
        );
        await StorageService.secureWrite(
          AppConstants.refreshTokenKey, 
          response.data['refreshToken']
        );
        return true;
      }

      return false;
    } catch (e) {
      print('刷新令牌过程中发生错误: $e');
      return false;
    }
  }

  /// 处理HTTP错误
  static String _handleError(dynamic error) {
    String errorDescription = '';

    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.cancel:
          errorDescription = '请求被取消';
          break;
        case DioExceptionType.connectionTimeout:
          errorDescription = '连接超时';
          break;
        case DioExceptionType.receiveTimeout:
          errorDescription = '接收数据超时';
          break;
        case DioExceptionType.sendTimeout:
          errorDescription = '发送数据超时';
          break;
        case DioExceptionType.badResponse:
          if (error.response?.data != null && error.response?.data['message'] != null) {
            errorDescription = error.response?.data['message'];
          } else {
            errorDescription = '服务器返回错误: ${error.response?.statusCode}';
          }
          break;
        case DioExceptionType.connectionError:
          errorDescription = '网络连接错误';
          break;
        case DioExceptionType.badCertificate:
          errorDescription = '服务器SSL证书验证失败';
          break;
        case DioExceptionType.unknown:
          if (error.error is SocketException) {
            errorDescription = '网络连接失败，请检查网络设置';
          } else {
            errorDescription = '未知错误: ${error.error}';
          }
          break;
        default:
          errorDescription = '发生未知错误';
      }
    } else {
      errorDescription = '发生错误: $error';
    }

    return errorDescription;
  }

  // HTTP请求方法

  /// GET请求
  static Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// POST请求
  static Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// PUT请求
  static Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// DELETE请求
  static Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// PATCH请求
  static Future<Response> patch(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }

  /// 下载文件
  static Future<Response> download(
    String url,
    String savePath, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    Function(int, int)? onReceiveProgress,
  }) async {
    try {
      return await _dio.download(
        url,
        savePath,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
    } catch (e) {
      throw _handleError(e);
    }
  }
}
