import {
  Injectable,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Transaction, TransactionType, TransactionStatus } from './entities/transaction.entity';
import { TransactionLog } from './entities/transaction-log.entity';

@Injectable()
export class TransactionsService {
  private readonly logger = new Logger(TransactionsService.name);

  constructor(
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(TransactionLog)
    private transactionLogRepository: Repository<TransactionLog>,
  ) {}

  /**
   * 获取用户交易历史
   */
  async getUserTransactions(
    userId: string,
    page: number = 1,
    limit: number = 20,
    type?: TransactionType,
    status?: TransactionStatus,
  ): Promise<{
    transactions: Transaction[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const queryBuilder = this.transactionRepository
      .createQueryBuilder('transaction')
      .leftJoinAndSelect('transaction.fromUser', 'fromUser')
      .leftJoinAndSelect('transaction.toUser', 'toUser')
      .where('transaction.fromUserId = :userId OR transaction.toUserId = :userId', { userId })
      .orderBy('transaction.createdAt', 'DESC');

    if (type) {
      queryBuilder.andWhere('transaction.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('transaction.status = :status', { status });
    }

    const [transactions, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      transactions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 根据ID获取交易详情
   */
  async getTransactionById(transactionId: string): Promise<Transaction> {
    const transaction = await this.transactionRepository.findOne({
      where: { id: transactionId },
      relations: ['fromUser', 'toUser', 'fromWallet', 'toWallet', 'logs'],
    });

    if (!transaction) {
      throw new NotFoundException('交易不存在');
    }

    return transaction;
  }

  /**
   * 根据交易号获取交易详情
   */
  async getTransactionByNo(transactionNo: string): Promise<Transaction> {
    const transaction = await this.transactionRepository.findOne({
      where: { transactionNo },
      relations: ['fromUser', 'toUser', 'fromWallet', 'toWallet', 'logs'],
    });

    if (!transaction) {
      throw new NotFoundException('交易不存在');
    }

    return transaction;
  }

  /**
   * 获取用户交易统计
   */
  async getUserTransactionStats(userId: string): Promise<{
    totalTransactions: number;
    totalIncome: number;
    totalExpense: number;
    pendingTransactions: number;
    completedTransactions: number;
    failedTransactions: number;
  }> {
    const [
      totalTransactions,
      incomeResult,
      expenseResult,
      pendingTransactions,
      completedTransactions,
      failedTransactions,
    ] = await Promise.all([
      // 总交易数
      this.transactionRepository.count({
        where: [
          { fromUserId: userId },
          { toUserId: userId },
        ],
      }),
      // 总收入
      this.transactionRepository
        .createQueryBuilder('transaction')
        .select('SUM(transaction.amount)', 'total')
        .where('transaction.toUserId = :userId AND transaction.status = :status', {
          userId,
          status: TransactionStatus.COMPLETED,
        })
        .getRawOne(),
      // 总支出
      this.transactionRepository
        .createQueryBuilder('transaction')
        .select('SUM(transaction.amount + transaction.fee)', 'total')
        .where('transaction.fromUserId = :userId AND transaction.status = :status', {
          userId,
          status: TransactionStatus.COMPLETED,
        })
        .getRawOne(),
      // 待处理交易数
      this.transactionRepository.count({
        where: [
          { fromUserId: userId, status: TransactionStatus.PENDING },
          { toUserId: userId, status: TransactionStatus.PENDING },
        ],
      }),
      // 已完成交易数
      this.transactionRepository.count({
        where: [
          { fromUserId: userId, status: TransactionStatus.COMPLETED },
          { toUserId: userId, status: TransactionStatus.COMPLETED },
        ],
      }),
      // 失败交易数
      this.transactionRepository.count({
        where: [
          { fromUserId: userId, status: TransactionStatus.FAILED },
          { toUserId: userId, status: TransactionStatus.FAILED },
        ],
      }),
    ]);

    return {
      totalTransactions,
      totalIncome: parseFloat(incomeResult?.total || '0'),
      totalExpense: parseFloat(expenseResult?.total || '0'),
      pendingTransactions,
      completedTransactions,
      failedTransactions,
    };
  }

  /**
   * 添加交易日志
   */
  async addTransactionLog(
    transactionId: string,
    status: TransactionStatus,
    message?: string,
  ): Promise<TransactionLog> {
    const log = this.transactionLogRepository.create({
      transactionId,
      status,
      message,
    });

    await this.transactionLogRepository.save(log);
    return log;
  }

  /**
   * 获取交易日志
   */
  async getTransactionLogs(transactionId: string): Promise<TransactionLog[]> {
    return this.transactionLogRepository.find({
      where: { transactionId },
      order: { createdAt: 'ASC' },
    });
  }
}
