import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';

import { TransactionsService } from './transactions.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { TransactionType, TransactionStatus } from './entities/transaction.entity';

@ApiTags('transactions')
@Controller('transactions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class TransactionsController {
  constructor(private readonly transactionsService: TransactionsService) {}

  @Get()
  @ApiOperation({ summary: '获取用户交易历史' })
  @ApiQuery({ name: 'page', required: false, example: 1 })
  @ApiQuery({ name: 'limit', required: false, example: 20 })
  @ApiQuery({ name: 'type', required: false, enum: TransactionType })
  @ApiQuery({ name: 'status', required: false, enum: TransactionStatus })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserTransactions(
    @CurrentUser() user: User,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('type') type?: TransactionType,
    @Query('status') status?: TransactionStatus,
  ) {
    return this.transactionsService.getUserTransactions(
      user.id,
      page,
      limit,
      type,
      status,
    );
  }

  @Get('stats')
  @ApiOperation({ summary: '获取用户交易统计' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserTransactionStats(@CurrentUser() user: User) {
    return this.transactionsService.getUserTransactionStats(user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取交易详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTransactionById(@Param('id') transactionId: string) {
    return this.transactionsService.getTransactionById(transactionId);
  }

  @Get(':id/logs')
  @ApiOperation({ summary: '获取交易日志' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getTransactionLogs(@Param('id') transactionId: string) {
    return this.transactionsService.getTransactionLogs(transactionId);
  }
}
