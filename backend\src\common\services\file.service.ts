import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class FileService {
  private readonly logger = new Logger(FileService.name);

  constructor(private configService: ConfigService) {}

  /**
   * 上传文件
   */
  async uploadFile(
    file: Express.Multer.File,
    folder: string = 'uploads',
  ): Promise<string> {
    const provider = this.configService.get<string>('app.services.storage.provider');
    
    try {
      switch (provider) {
        case 'aws':
          return await this.uploadToAws(file, folder);
        case 'local':
        default:
          return await this.uploadToLocal(file, folder);
      }
    } catch (error) {
      this.logger.error('文件上传失败', error);
      throw new Error('文件上传失败');
    }
  }

  /**
   * 上传到AWS S3
   */
  private async uploadToAws(
    file: Express.Multer.File,
    folder: string,
  ): Promise<string> {
    // 这里实现AWS S3上传
    // 需要安装 aws-sdk 包
    
    this.logger.debug(`AWS S3上传: ${file.originalname}`);
    
    // 实际实现示例：
    /*
    const AWS = require('aws-sdk');
    
    const s3 = new AWS.S3({
      accessKeyId: this.configService.get<string>('app.services.storage.accessKeyId'),
      secretAccessKey: this.configService.get<string>('app.services.storage.secretAccessKey'),
      region: this.configService.get<string>('app.services.storage.region'),
    });

    const key = `${folder}/${Date.now()}-${file.originalname}`;
    
    const params = {
      Bucket: this.configService.get<string>('app.services.storage.bucket'),
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
      ACL: 'public-read',
    };

    const result = await s3.upload(params).promise();
    return result.Location;
    */
    
    // 模拟返回URL
    return `https://example-bucket.s3.amazonaws.com/${folder}/${Date.now()}-${file.originalname}`;
  }

  /**
   * 上传到本地存储
   */
  private async uploadToLocal(
    file: Express.Multer.File,
    folder: string,
  ): Promise<string> {
    const fs = require('fs').promises;
    const path = require('path');
    
    const uploadDir = path.join(process.cwd(), 'uploads', folder);
    const filename = `${Date.now()}-${file.originalname}`;
    const filepath = path.join(uploadDir, filename);
    
    // 确保目录存在
    await fs.mkdir(uploadDir, { recursive: true });
    
    // 保存文件
    await fs.writeFile(filepath, file.buffer);
    
    // 返回访问URL
    const baseUrl = this.configService.get<string>('app.baseUrl') || 'http://localhost:3000';
    return `${baseUrl}/uploads/${folder}/${filename}`;
  }

  /**
   * 删除文件
   */
  async deleteFile(fileUrl: string): Promise<void> {
    const provider = this.configService.get<string>('app.services.storage.provider');
    
    try {
      switch (provider) {
        case 'aws':
          await this.deleteFromAws(fileUrl);
          break;
        case 'local':
        default:
          await this.deleteFromLocal(fileUrl);
          break;
      }
      
      this.logger.log(`文件删除成功: ${fileUrl}`);
    } catch (error) {
      this.logger.error(`文件删除失败: ${fileUrl}`, error);
    }
  }

  /**
   * 从AWS S3删除文件
   */
  private async deleteFromAws(fileUrl: string): Promise<void> {
    // 从URL中提取key
    const url = new URL(fileUrl);
    const key = url.pathname.substring(1); // 移除开头的 '/'
    
    this.logger.debug(`AWS S3删除: ${key}`);
    
    // 实际实现示例：
    /*
    const AWS = require('aws-sdk');
    
    const s3 = new AWS.S3({
      accessKeyId: this.configService.get<string>('app.services.storage.accessKeyId'),
      secretAccessKey: this.configService.get<string>('app.services.storage.secretAccessKey'),
      region: this.configService.get<string>('app.services.storage.region'),
    });

    const params = {
      Bucket: this.configService.get<string>('app.services.storage.bucket'),
      Key: key,
    };

    await s3.deleteObject(params).promise();
    */
  }

  /**
   * 从本地存储删除文件
   */
  private async deleteFromLocal(fileUrl: string): Promise<void> {
    const fs = require('fs').promises;
    const path = require('path');
    
    // 从URL中提取文件路径
    const url = new URL(fileUrl);
    const filepath = path.join(process.cwd(), url.pathname);
    
    try {
      await fs.unlink(filepath);
    } catch (error) {
      // 文件不存在时忽略错误
      if (error.code !== 'ENOENT') {
        throw error;
      }
    }
  }

  /**
   * 验证文件类型
   */
  validateFileType(file: Express.Multer.File, allowedTypes: string[]): boolean {
    const fileExtension = file.originalname.split('.').pop()?.toLowerCase();
    return fileExtension ? allowedTypes.includes(fileExtension) : false;
  }

  /**
   * 验证文件大小
   */
  validateFileSize(file: Express.Multer.File, maxSize: number): boolean {
    return file.size <= maxSize;
  }

  /**
   * 生成安全的文件名
   */
  generateSafeFilename(originalName: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 8);
    const extension = originalName.split('.').pop();
    const baseName = originalName.split('.').slice(0, -1).join('.');
    
    // 清理文件名，只保留字母、数字、下划线和连字符
    const safeName = baseName.replace(/[^a-zA-Z0-9_-]/g, '_');
    
    return `${timestamp}_${randomString}_${safeName}.${extension}`;
  }

  /**
   * 获取文件MIME类型
   */
  getMimeType(filename: string): string {
    const extension = filename.split('.').pop()?.toLowerCase();
    
    const mimeTypes: { [key: string]: string } = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };
    
    return mimeTypes[extension || ''] || 'application/octet-stream';
  }
}
