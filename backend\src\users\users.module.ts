import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { UsersController } from './users.controller';
import { UsersService } from './users.service';

import { User } from './entities/user.entity';
import { UserDevice } from './entities/user-device.entity';
import { KycDocument } from './entities/kyc-document.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, UserDevice, KycDocument]),
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
