# 集成测试

此目录包含后端集成测试。集成测试用于验证多个组件协同工作的功能，通常是控制器与服务层的集成。

## 目录结构

集成测试应按模块组织：

```
/integration
  /users
    users.controller.spec.ts
  /transactions
    transactions.controller.spec.ts
```

## 测试规范

- 测试文件命名应以`.spec.ts`结尾
- 测试应尽量使用实际的服务实现，但模拟外部依赖
- 使用`@nestjs/testing`的`Test`模块创建测试模块
- 为测试提供专用的配置和环境变量

## 运行测试

```bash
# 运行所有集成测试
npm run test:integration

# 运行特定测试文件
npm run test:integration -- users.controller
```
