import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, EntityManager } from 'typeorm';
import { ConfigService } from '@nestjs/config';

import { PaymentCode } from './entities/payment-code.entity';
import { RedPacket } from './entities/red-packet.entity';
import { RedPacketClaim } from './entities/red-packet-claim.entity';
import { Transaction, TransactionType, TransactionStatus } from '../transactions/entities/transaction.entity';
import { User } from '../users/entities/user.entity';
import { WalletsService } from '../wallets/wallets.service';
import { CryptoService } from '../common/services/crypto.service';

import { CreatePaymentCodeDto } from './dto/create-payment-code.dto';
import { CreateRedPacketDto } from './dto/create-red-packet.dto';
import { PayByCodeDto } from './dto/pay-by-code.dto';

@Injectable()
export class PaymentsService {
  private readonly logger = new Logger(PaymentsService.name);

  constructor(
    @InjectRepository(PaymentCode)
    private paymentCodeRepository: Repository<PaymentCode>,
    @InjectRepository(RedPacket)
    private redPacketRepository: Repository<RedPacket>,
    @InjectRepository(RedPacketClaim)
    private redPacketClaimRepository: Repository<RedPacketClaim>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private dataSource: DataSource,
    private configService: ConfigService,
    private cryptoService: CryptoService,
    private walletsService: WalletsService,
  ) {}

  /**
   * 创建收款码
   */
  async createPaymentCode(
    userId: string,
    createPaymentCodeDto: CreatePaymentCodeDto,
  ): Promise<PaymentCode> {
    const { amount, currency, description, expiresIn, maxUsage } = createPaymentCodeDto;

    // 验证用户存在
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 生成唯一的支付码
    const code = this.cryptoService.generatePaymentCode();

    // 计算过期时间
    let expiresAt: Date | null = null;
    if (expiresIn && expiresIn > 0) {
      expiresAt = new Date(Date.now() + expiresIn * 1000);
    }

    const paymentCode = this.paymentCodeRepository.create({
      userId,
      code,
      amount,
      currency: currency || 'CNY',
      description,
      expiresAt,
      maxUsage: maxUsage || 1,
    });

    await this.paymentCodeRepository.save(paymentCode);

    this.logger.log(`收款码创建成功: 用户 ${userId}, 代码 ${code}`);
    return paymentCode;
  }

  /**
   * 通过收款码支付
   */
  async payByCode(
    payerUserId: string,
    payByCodeDto: PayByCodeDto,
  ): Promise<Transaction> {
    const { code, amount, description } = payByCodeDto;

    // 查找收款码
    const paymentCode = await this.paymentCodeRepository.findOne({
      where: { code },
      relations: ['user'],
    });

    if (!paymentCode) {
      throw new NotFoundException('收款码不存在');
    }

    if (!paymentCode.isUsable) {
      throw new BadRequestException('收款码不可用');
    }

    if (!paymentCode.canPayAmount(amount)) {
      throw new BadRequestException('支付金额不匹配');
    }

    // 检查是否自己支付给自己
    if (payerUserId === paymentCode.userId) {
      throw new BadRequestException('不能支付给自己');
    }

    // 执行转账
    const transaction = await this.walletsService.transfer(
      payerUserId,
      paymentCode.userId,
      amount,
      paymentCode.currency,
      description || paymentCode.description,
    );

    // 更新收款码使用次数
    paymentCode.use();
    await this.paymentCodeRepository.save(paymentCode);

    this.logger.log(
      `收款码支付成功: ${payerUserId} -> ${paymentCode.userId}, 金额: ${amount}, 代码: ${code}`,
    );

    return transaction;
  }

  /**
   * 创建红包
   */
  async createRedPacket(
    senderId: string,
    createRedPacketDto: CreateRedPacketDto,
  ): Promise<RedPacket> {
    const { totalAmount, totalCount, currency, message, expiresIn, isRandom } = createRedPacketDto;

    // 验证红包参数
    const maxAmount = this.configService.get<number>('app.business.redPacket.maxAmount');
    const maxCount = this.configService.get<number>('app.business.redPacket.maxCount');

    if (totalAmount > maxAmount) {
      throw new BadRequestException(`红包总金额不能超过 ${maxAmount} 元`);
    }

    if (totalCount > maxCount) {
      throw new BadRequestException(`红包个数不能超过 ${maxCount} 个`);
    }

    if (totalAmount / totalCount < 0.01) {
      throw new BadRequestException('每个红包金额不能少于 0.01 元');
    }

    // 验证用户存在
    const user = await this.userRepository.findOne({ where: { id: senderId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 检查余额
    const wallet = await this.walletsService.getUserWallet(senderId, currency || 'CNY');
    if (!wallet.hasAvailableBalance(totalAmount)) {
      throw new BadRequestException('余额不足');
    }

    // 计算过期时间
    const defaultExpiresIn = this.configService.get<number>('app.business.redPacket.expiresIn');
    const expirationTime = expiresIn || defaultExpiresIn;
    const expiresAt = new Date(Date.now() + expirationTime * 1000);

    return this.dataSource.transaction(async (manager: EntityManager) => {
      // 扣减发送方余额
      wallet.deductBalance(totalAmount);
      await manager.save(wallet);

      // 创建红包
      const redPacket = manager.create(RedPacket, {
        senderId,
        totalAmount,
        totalCount,
        remainingAmount: totalAmount,
        remainingCount: totalCount,
        currency: currency || 'CNY',
        message,
        expiresAt,
        isRandom: isRandom !== false, // 默认为随机红包
      });

      await manager.save(redPacket);

      this.logger.log(
        `红包创建成功: 发送者 ${senderId}, 总金额: ${totalAmount}, 个数: ${totalCount}`,
      );

      return redPacket;
    });
  }

  /**
   * 领取红包
   */
  async claimRedPacket(userId: string, redPacketId: string): Promise<{
    claim: RedPacketClaim;
    transaction: Transaction;
  }> {
    // 查找红包
    const redPacket = await this.redPacketRepository.findOne({
      where: { id: redPacketId },
      relations: ['sender'],
    });

    if (!redPacket) {
      throw new NotFoundException('红包不存在');
    }

    if (!redPacket.canClaimBy(userId)) {
      throw new BadRequestException('无法领取此红包');
    }

    // 检查是否已经领取过
    const existingClaim = await this.redPacketClaimRepository.findOne({
      where: { redPacketId, userId },
    });

    if (existingClaim) {
      throw new BadRequestException('您已经领取过此红包');
    }

    return this.dataSource.transaction(async (manager: EntityManager) => {
      // 计算红包金额
      const amount = redPacket.calculateAmount();

      // 领取红包
      redPacket.claim(amount);
      await manager.save(redPacket);

      // 创建领取记录
      const claim = manager.create(RedPacketClaim, {
        redPacketId,
        userId,
        amount,
      });

      await manager.save(claim);

      // 增加用户余额
      const wallet = await this.walletsService.getUserWallet(userId, redPacket.currency);
      wallet.addBalance(amount);
      await manager.save(wallet);

      // 创建交易记录
      const transaction = manager.create(Transaction, {
        transactionNo: this.cryptoService.generateTransactionNo(),
        fromUserId: redPacket.senderId,
        toUserId: userId,
        toWalletId: wallet.id,
        amount,
        fee: 0,
        currency: redPacket.currency,
        type: TransactionType.RED_PACKET_CLAIM,
        status: TransactionStatus.COMPLETED,
        description: `领取红包: ${redPacket.message || '恭喜发财'}`,
        referenceId: redPacketId,
        processedAt: new Date(),
      });

      await manager.save(transaction);

      this.logger.log(
        `红包领取成功: 用户 ${userId}, 红包 ${redPacketId}, 金额: ${amount}`,
      );

      return { claim, transaction };
    });
  }

  /**
   * 获取收款码详情
   */
  async getPaymentCode(code: string): Promise<PaymentCode> {
    const paymentCode = await this.paymentCodeRepository.findOne({
      where: { code },
      relations: ['user'],
    });

    if (!paymentCode) {
      throw new NotFoundException('收款码不存在');
    }

    return paymentCode;
  }

  /**
   * 获取用户的收款码列表
   */
  async getUserPaymentCodes(userId: string): Promise<PaymentCode[]> {
    return this.paymentCodeRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 获取红包详情
   */
  async getRedPacket(redPacketId: string): Promise<RedPacket> {
    const redPacket = await this.redPacketRepository.findOne({
      where: { id: redPacketId },
      relations: ['sender', 'claims', 'claims.user'],
    });

    if (!redPacket) {
      throw new NotFoundException('红包不存在');
    }

    return redPacket;
  }

  /**
   * 获取用户发送的红包列表
   */
  async getUserSentRedPackets(userId: string): Promise<RedPacket[]> {
    return this.redPacketRepository.find({
      where: { senderId: userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * 获取用户领取的红包列表
   */
  async getUserClaimedRedPackets(userId: string): Promise<RedPacketClaim[]> {
    return this.redPacketClaimRepository.find({
      where: { userId },
      relations: ['redPacket', 'redPacket.sender'],
      order: { claimedAt: 'DESC' },
    });
  }

  /**
   * 停用收款码
   */
  async deactivatePaymentCode(userId: string, code: string): Promise<void> {
    const paymentCode = await this.paymentCodeRepository.findOne({
      where: { code, userId },
    });

    if (!paymentCode) {
      throw new NotFoundException('收款码不存在');
    }

    paymentCode.deactivate();
    await this.paymentCodeRepository.save(paymentCode);

    this.logger.log(`收款码已停用: 用户 ${userId}, 代码 ${code}`);
  }
}
