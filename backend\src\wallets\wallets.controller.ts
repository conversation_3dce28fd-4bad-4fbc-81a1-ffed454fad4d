import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';

import { WalletsService } from './wallets.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('wallets')
@Controller('wallets')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class WalletsController {
  constructor(private readonly walletsService: WalletsService) {}

  @Get()
  @ApiOperation({ summary: '获取用户所有钱包' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getUserWallets(@CurrentUser() user: User) {
    return this.walletsService.getUserWallets(user.id);
  }

  @Get('balance')
  @ApiOperation({ summary: '获取钱包余额' })
  @ApiQuery({ name: 'currency', required: false, example: 'CNY' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getBalance(
    @CurrentUser() user: User,
    @Query('currency') currency: string = 'CNY',
  ) {
    return this.walletsService.getBalance(user.id, currency);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取钱包统计信息' })
  @ApiQuery({ name: 'currency', required: false, example: 'CNY' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getWalletStats(
    @CurrentUser() user: User,
    @Query('currency') currency: string = 'CNY',
  ) {
    return this.walletsService.getWalletStats(user.id, currency);
  }

  @Post('transfer')
  @ApiOperation({ summary: '转账' })
  @ApiResponse({ status: 201, description: '转账成功' })
  async transfer(
    @CurrentUser() user: User,
    @Body() transferDto: {
      toUserId: string;
      amount: number;
      currency?: string;
      description?: string;
    },
  ) {
    return this.walletsService.transfer(
      user.id,
      transferDto.toUserId,
      transferDto.amount,
      transferDto.currency,
      transferDto.description,
    );
  }
}
