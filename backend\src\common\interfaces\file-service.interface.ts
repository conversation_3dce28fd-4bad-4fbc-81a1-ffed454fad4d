/**
 * 文件服务接口
 * 定义文件上传、下载、验证等操作
 */
export interface IFileService {
  /**
   * 上传文件
   * @param file 文件对象
   * @param path 存储路径
   * @returns 文件访问URL
   */
  uploadFile(file: any, path: string): Promise<string>;

  /**
   * 删除文件
   * @param path 文件路径
   */
  deleteFile(path: string): Promise<void>;

  /**
   * 验证文件类型
   * @param file 文件对象
   * @param allowedTypes 允许的文件类型数组
   * @returns 是否通过验证
   */
  validateFileType(file: any, allowedTypes: string[]): boolean;

  /**
   * 验证文件大小
   * @param file 文件对象
   * @param maxSize 最大文件大小(字节)
   * @returns 是否通过验证
   */
  validateFileSize(file: any, maxSize: number): boolean;

  /**
   * 获取文件访问URL
   * @param path 文件路径
   * @param expiresIn URL过期时间(秒)
   * @returns 文件访问URL
   */
  getFileUrl(path: string, expiresIn?: number): Promise<string>;
}
