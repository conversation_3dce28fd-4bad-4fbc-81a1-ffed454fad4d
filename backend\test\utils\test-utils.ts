import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from '../../src/app.module';
import { getConnection } from 'typeorm';
import * as request from 'supertest';

/**
 * 创建测试应用
 * @param imports 额外导入的模块
 * @returns 配置好的NestJS应用实例
 */
export async function createTestingApp(imports: any[] = []): Promise<INestApplication> {
  const moduleFixture: TestingModule = await Test.createTestingModule({
    imports: [AppModule, ...imports],
  }).compile();

  const app = moduleFixture.createNestApplication();
  await app.init();
  return app;
}

/**
 * 清理测试数据库
 * @param entities 要清理的实体列表
 */
export async function clearDatabase(entities: string[] = []): Promise<void> {
  const connection = getConnection();
  const entityNames = entities.length > 0 
    ? entities 
    : connection.entityMetadatas.map(entity => entity.name);

  for (const entityName of entityNames) {
    const repository = connection.getRepository(entityName);
    await repository.query(`DELETE FROM ${entityName}`);
  }
}

/**
 * 获取有效的认证令牌
 * @param app NestJS应用实例
 * @param credentials 登录凭据 {email, password}
 * @returns JWT令牌
 */
export async function getAuthToken(
  app: INestApplication,
  credentials = { email: '<EMAIL>', password: 'password123' }
): Promise<string> {
  const response = await request(app.getHttpServer())
    .post('/auth/login')
    .send(credentials)
    .expect(201);

  return response.body.accessToken;
}

/**
 * 创建带认证的请求对象
 * @param app NestJS应用实例
 * @param token JWT令牌
 * @returns 预配置的supertest请求对象
 */
export function authRequest(app: INestApplication, token: string) {
  return request(app.getHttpServer())
    .set('Authorization', `Bearer ${token}`);
}
