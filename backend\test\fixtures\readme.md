# 测试数据

此目录包含测试使用的固定数据和数据工厂。这些数据用于准备测试环境和构建测试场景。

## 目录结构

按领域模型组织：

```
/fixtures
  /users
    user.factory.ts
    user.seed.ts
  /transactions
    transaction.factory.ts
    transaction.seed.ts
```

## 使用指南

### 数据工厂

数据工厂用于快速创建测试对象实例，可以指定自定义属性覆盖默认值：

```typescript
// 使用示例
import { UserFactory } from './fixtures/users/user.factory';

// 创建默认用户
const defaultUser = UserFactory.create();

// 创建自定义用户
const customUser = UserFactory.create({
  email: '<EMAIL>',
  status: UserStatus.PENDING
});
```

### 种子数据

种子数据用于初始化测试数据库：

```typescript
// 使用示例
import { seedUsers } from './fixtures/users/user.seed';

// 初始化用户数据
await seedUsers(connection);
```
