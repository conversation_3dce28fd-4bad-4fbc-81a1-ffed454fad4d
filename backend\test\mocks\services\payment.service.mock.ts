/**
 * 支付方式枚举
 */
export enum PaymentMethod {
  ALIPAY = 'alipay',
  WECHAT = 'wechat',
  BANK_CARD = 'bank_card',
  BALANCE = 'balance'
}

/**
 * 支付结果接口
 */
export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  error?: string;
  data?: Record<string, any>;
}

/**
 * 支付服务接口
 */
export interface IPaymentService {
  createPayment(
    userId: string,
    amount: number,
    currency: string,
    method: PaymentMethod,
    metadata?: Record<string, any>
  ): Promise<{ paymentId: string; paymentUrl: string }>;

  confirmPayment(paymentId: string): Promise<PaymentResult>;

  cancelPayment(paymentId: string): Promise<void>;

  refund(
    transactionId: string,
    amount: number,
    reason?: string
  ): Promise<PaymentResult>;

  getPaymentStatus(paymentId: string): Promise<string>;
}

/**
 * 模拟支付服务
 * 用于在测试中替代实际的支付服务
 */
export const mockPaymentService: jest.Mocked<IPaymentService> = {
  createPayment: jest.fn().mockImplementation((userId, amount, currency, method, metadata) => {
    return Promise.resolve({
      paymentId: `PAY${Date.now()}`,
      paymentUrl: `https://payment.example.com/pay?id=PAY${Date.now()}`
    });
  }),

  confirmPayment: jest.fn().mockImplementation(paymentId => {
    return Promise.resolve({
      success: true,
      transactionId: `TRX${Date.now()}`,
      data: { time: new Date().toISOString() }
    });
  }),

  cancelPayment: jest.fn().mockImplementation(paymentId => {
    return Promise.resolve();
  }),

  refund: jest.fn().mockImplementation((transactionId, amount, reason) => {
    return Promise.resolve({
      success: true,
      transactionId: `REF${Date.now()}`,
      data: { time: new Date().toISOString() }
    });
  }),

  getPaymentStatus: jest.fn().mockImplementation(paymentId => {
    return Promise.resolve('completed');
  })
};

/**
 * 带错误处理的模拟支付服务
 * 可用于测试错误情况
 */
export const mockPaymentServiceWithErrors: jest.Mocked<IPaymentService> = {
  createPayment: jest.fn().mockRejectedValue(new Error('Failed to create payment')),

  confirmPayment: jest.fn().mockImplementation(paymentId => {
    return Promise.resolve({
      success: false,
      error: 'Payment confirmation failed',
      data: { errorCode: 'PAYMENT_FAILED' }
    });
  }),

  cancelPayment: jest.fn().mockRejectedValue(new Error('Failed to cancel payment')),

  refund: jest.fn().mockImplementation((transactionId, amount, reason) => {
    return Promise.resolve({
      success: false,
      error: 'Refund failed',
      data: { errorCode: 'REFUND_FAILED' }
    });
  }),

  getPaymentStatus: jest.fn().mockImplementation(paymentId => {
    return Promise.resolve('failed');
  })
};

/**
 * 重置所有模拟函数
 */
export function resetPaymentMocks(): void {
  mockPaymentService.createPayment.mockClear();
  mockPaymentService.confirmPayment.mockClear();
  mockPaymentService.cancelPayment.mockClear();
  mockPaymentService.refund.mockClear();
  mockPaymentService.getPaymentStatus.mockClear();
}
