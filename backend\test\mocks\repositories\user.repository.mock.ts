import { BaseMockRepository } from './base.repository.mock';
import { User, UserFactory } from '../../fixtures/users/user.factory';

/**
 * 模拟用户仓库
 * 继承基础模拟仓库，实现用户特定方法
 */
export class UserMockRepository extends BaseMockRepository<User> {
  /**
   * 根据邮箱查找用户
   * @param email 用户邮箱
   * @returns 用户对象或null
   */
  findByEmail(email: string): Promise<User | null> {
    return this.findOne({ email } as Partial<User>);
  }

  /**
   * 根据手机号查找用户
   * @param phone 手机号
   * @returns 用户对象或null
   */
  findByPhone(phone: string): Promise<User | null> {
    return this.findOne({ phone } as Partial<User>);
  }

  /**
   * 根据用户名查找用户
   * @param username 用户名
   * @returns 用户对象或null
   */
  findByUsername(username: string): Promise<User | null> {
    return this.findOne({ username } as Partial<User>);
  }

  /**
   * 初始化测试数据
   * @param count 用户数量
   */
  seed(count = 5): void {
    this.entities = UserFactory.createMany(count);
  }

  /**
   * 查找活跃用户
   * @returns 活跃用户列表
   */
  findActiveUsers(): Promise<User[]> {
    return this.findBy({ status: 'active' } as Partial<User>);
  }

  /**
   * 按创建时间排序查找用户
   * @param limit 限制数量
   * @returns 排序后的用户列表
   */
  findRecentUsers(limit = 10): Promise<User[]> {
    const sorted = [...this.entities].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    return Promise.resolve(sorted.slice(0, limit));
  }

  /**
   * 更新用户状态
   * @param userId 用户ID
   * @param status 新状态
   * @returns 更新后的用户
   */
  async updateStatus(userId: string, status: string): Promise<User | null> {
    const user = await this.findOneById(userId);
    if (!user) return null;

    return this.save({ ...user, status, updatedAt: new Date() });
  }
}

/**
 * 创建预填充的用户仓库
 * @param initialUsers 初始用户数组
 * @returns 用户仓库实例
 */
export function createUserRepository(initialUsers: User[] = []): UserMockRepository {
  const repo = new UserMockRepository();

  if (initialUsers.length > 0) {
    repo.initialize(initialUsers);
  } else {
    // 使用默认测试数据
    repo.seed(5);
  }

  return repo;
}
