# 通用忽略文件
.DS_Store
.vscode/
.idea/
*.log
*.tmp
*.temp
.env
.env.local
.env.production
.env.staging

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Flutter
frontend/.dart_tool/
frontend/.flutter-plugins
frontend/.flutter-plugins-dependencies
frontend/.packages
frontend/.pub-cache/
frontend/.pub/
frontend/build/
frontend/ios/Flutter/Generated.xcconfig
frontend/ios/Flutter/flutter_export_environment.sh
frontend/ios/Runner/GeneratedPluginRegistrant.*
frontend/android/app/debug
frontend/android/app/profile
frontend/android/app/release
frontend/android/.gradle
frontend/android/captures/
frontend/android/gradlew
frontend/android/gradlew.bat
frontend/android/local.properties
frontend/android/**/GeneratedPluginRegistrant.java
frontend/android/key.properties
frontend/*.g.dart

# iOS
frontend/ios/Pods/
frontend/ios/.symlinks/
frontend/ios/Flutter/App.framework
frontend/ios/Flutter/Flutter.framework
frontend/ios/Flutter/Flutter.podspec
frontend/ios/Flutter/Generated.xcconfig
frontend/ios/Flutter/app.flx
frontend/ios/Flutter/app.zip
frontend/ios/Flutter/flutter_assets/
frontend/ios/ServiceDefinitions.json
frontend/ios/Runner/GeneratedPluginRegistrant.*

# Android
frontend/android/app/debug
frontend/android/app/profile
frontend/android/app/release
frontend/android/.gradle
frontend/android/captures/
frontend/android/gradlew
frontend/android/gradlew.bat
frontend/android/local.properties
frontend/android/**/GeneratedPluginRegistrant.java

# Web
frontend/web/

# Backend
backend/dist/
backend/build/
backend/coverage/
backend/.nyc_output/

# 数据库
*.db
*.sqlite
*.sqlite3

# Docker
.docker/

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/

# 证书和密钥
*.pem
*.key
*.crt
*.p12
*.jks
ssl/

# 配置文件（包含敏感信息）
config/production.json
config/staging.json
secrets/

# 测试覆盖率报告
coverage/
.nyc_output/

# 构建产物
dist/
build/
out/

# 依赖锁定文件
package-lock.json
yarn.lock
pubspec.lock

# IDE配置
.vscode/settings.json
.idea/
*.swp
*.swo
*~

# 操作系统
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar

# 文档生成
docs/build/
site/
