/// 应用常量定义
class AppConstants {
  // 应用信息
  static const String appName = 'Wallet';
  static const String appVersion = '1.0.0';
  static const String appDescription = '安全便捷的数字钱包';
  
  // API配置
  static const String baseUrl = 'http://localhost:3000/api/v1';
  static const String wsBaseUrl = 'ws://localhost:3000';
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // 存储键名
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userInfoKey = 'user_info';
  static const String biometricEnabledKey = 'biometric_enabled';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // 页面路由
  static const String splashRoute = '/';
  static const String loginRoute = '/login';
  static const String registerRoute = '/register';
  static const String homeRoute = '/home';
  static const String profileRoute = '/profile';
  static const String walletRoute = '/wallet';
  static const String transferRoute = '/transfer';
  static const String scanRoute = '/scan';
  static const String historyRoute = '/history';
  static const String settingsRoute = '/settings';
  static const String securityRoute = '/security';
  static const String cardsRoute = '/cards';
  static const String notificationsRoute = '/notifications';
  
  // 动画时长
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // UI尺寸
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 12.0;
  static const double largeBorderRadius = 20.0;
  static const double cardBorderRadius = 16.0;
  
  // 字体大小
  static const double titleFontSize = 24.0;
  static const double headlineFontSize = 20.0;
  static const double bodyFontSize = 16.0;
  static const double captionFontSize = 14.0;
  static const double smallFontSize = 12.0;
  
  // 图标大小
  static const double smallIconSize = 16.0;
  static const double defaultIconSize = 24.0;
  static const double largeIconSize = 32.0;
  static const double extraLargeIconSize = 48.0;
  
  // 业务常量
  static const double minTransferAmount = 0.01;
  static const double maxTransferAmount = 50000.00;
  static const double maxDailyTransferAmount = 100000.00;
  static const int maxTransferDescriptionLength = 100;
  static const int maxRedPacketCount = 100;
  static const double maxRedPacketAmount = 200.00;
  
  // 验证规则
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 20;
  static const int smsCodeLength = 6;
  static const Duration smsCodeExpiry = Duration(minutes: 5);
  static const int maxLoginAttempts = 5;
  static const Duration accountLockDuration = Duration(minutes: 30);
  
  // 正则表达式
  static const String phoneRegex = r'^1[3-9]\d{9}$';
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String passwordRegex = r'^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$';
  
  // 错误消息
  static const String networkErrorMessage = '网络连接失败，请检查网络设置';
  static const String serverErrorMessage = '服务器错误，请稍后重试';
  static const String unknownErrorMessage = '未知错误，请稍后重试';
  static const String invalidPhoneMessage = '请输入正确的手机号码';
  static const String invalidEmailMessage = '请输入正确的邮箱地址';
  static const String invalidPasswordMessage = '密码必须包含字母和数字，长度6-20位';
  static const String passwordMismatchMessage = '两次输入的密码不一致';
  static const String insufficientBalanceMessage = '余额不足';
  static const String transferAmountExceedsLimitMessage = '转账金额超出限制';
  
  // 成功消息
  static const String loginSuccessMessage = '登录成功';
  static const String registerSuccessMessage = '注册成功';
  static const String transferSuccessMessage = '转账成功';
  static const String passwordChangeSuccessMessage = '密码修改成功';
  static const String profileUpdateSuccessMessage = '个人信息更新成功';
  
  // 确认消息
  static const String logoutConfirmMessage = '确定要退出登录吗？';
  static const String deleteAccountConfirmMessage = '确定要删除账户吗？此操作不可恢复';
  static const String transferConfirmMessage = '确定要转账吗？';
  
  // 按钮文本
  static const String confirmText = '确定';
  static const String cancelText = '取消';
  static const String submitText = '提交';
  static const String saveText = '保存';
  static const String deleteText = '删除';
  static const String editText = '编辑';
  static const String retryText = '重试';
  static const String refreshText = '刷新';
  static const String loginText = '登录';
  static const String registerText = '注册';
  static const String logoutText = '退出登录';
  static const String nextText = '下一步';
  static const String previousText = '上一步';
  static const String finishText = '完成';
  static const String skipText = '跳过';
  
  // 占位符文本
  static const String phonePlaceholder = '请输入手机号';
  static const String emailPlaceholder = '请输入邮箱地址';
  static const String passwordPlaceholder = '请输入密码';
  static const String confirmPasswordPlaceholder = '请再次输入密码';
  static const String smsCodePlaceholder = '请输入验证码';
  static const String transferAmountPlaceholder = '请输入转账金额';
  static const String transferDescriptionPlaceholder = '添加备注（可选）';
  static const String searchPlaceholder = '搜索';
  
  // 货币符号
  static const String cnySymbol = '¥';
  static const String usdSymbol = '\$';
  static const String eurSymbol = '€';
  
  // 日期格式
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm:ss';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String displayDateFormat = 'MM月dd日';
  static const String displayTimeFormat = 'HH:mm';
  static const String displayDateTimeFormat = 'MM月dd日 HH:mm';
  
  // 文件路径
  static const String imagesPath = 'assets/images/';
  static const String iconsPath = 'assets/icons/';
  static const String animationsPath = 'assets/animations/';
  static const String fontsPath = 'assets/fonts/';
  
  // 图片资源
  static const String logoImage = '${imagesPath}logo.png';
  static const String avatarPlaceholder = '${imagesPath}avatar_placeholder.png';
  static const String emptyStateImage = '${imagesPath}empty_state.png';
  static const String errorStateImage = '${imagesPath}error_state.png';
  
  // 动画资源
  static const String loadingAnimation = '${animationsPath}loading.json';
  static const String successAnimation = '${animationsPath}success.json';
  static const String errorAnimation = '${animationsPath}error.json';
  
  // 环境配置
  static const bool isDebug = true;
  static const bool enableLogging = true;
  static const bool enableCrashlytics = false;
  static const bool enableAnalytics = false;
}
