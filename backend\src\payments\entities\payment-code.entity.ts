import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('payment_codes')
@Index(['code'], { unique: true })
@Index(['userId'])
export class PaymentCode {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ length: 100, unique: true })
  code: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    nullable: true,
    transformer: {
      to: (value: number) => value,
      from: (value: string) => value ? parseFloat(value) : null,
    },
  })
  amount: number;

  @Column({ length: 10, default: 'CNY' })
  currency: string;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'expires_at', type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'usage_count', default: 0 })
  usageCount: number;

  @Column({ name: 'max_usage', default: 1 })
  maxUsage: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // 虚拟属性
  get isExpired(): boolean {
    return this.expiresAt && this.expiresAt < new Date();
  }

  get isUsable(): boolean {
    return (
      this.isActive &&
      !this.isExpired &&
      this.usageCount < this.maxUsage
    );
  }

  get remainingUsage(): number {
    return Math.max(0, this.maxUsage - this.usageCount);
  }

  // 方法
  /**
   * 使用支付码
   */
  use(): void {
    if (!this.isUsable) {
      throw new Error('支付码不可用');
    }
    this.usageCount += 1;
  }

  /**
   * 停用支付码
   */
  deactivate(): void {
    this.isActive = false;
  }

  /**
   * 激活支付码
   */
  activate(): void {
    this.isActive = true;
  }

  /**
   * 设置过期时间
   */
  setExpiration(expiresAt: Date): void {
    this.expiresAt = expiresAt;
  }

  /**
   * 检查是否可以支付指定金额
   */
  canPayAmount(payAmount: number): boolean {
    if (!this.isUsable) {
      return false;
    }
    
    // 如果设置了固定金额，必须匹配
    if (this.amount !== null && this.amount !== payAmount) {
      return false;
    }
    
    return true;
  }
}
