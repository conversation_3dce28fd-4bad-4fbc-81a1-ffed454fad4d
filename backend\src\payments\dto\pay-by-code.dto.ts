import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNumber,
  IsOptional,
  IsNotEmpty,
  IsPositive,
  Max,
  Length,
} from 'class-validator';

export class PayByCodeDto {
  @ApiProperty({
    description: '收款码',
    example: 'PAY1234567890ABCDEF',
  })
  @IsString()
  @IsNotEmpty()
  @Length(10, 100)
  code: string;

  @ApiProperty({
    description: '支付金额',
    example: 100.00,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  @Max(50000)
  amount: number;

  @ApiProperty({
    description: '支付备注',
    example: '商品付款',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(1, 100)
  description?: string;
}
