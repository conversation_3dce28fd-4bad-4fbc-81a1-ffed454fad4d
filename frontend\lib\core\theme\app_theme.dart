import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

/// 应用主题配置
class AppTheme {
  // 主色调
  static const Color primaryColor = Color(0xFF007AFF);
  static const Color primaryLightColor = Color(0xFF5AC8FA);
  static const Color primaryDarkColor = Color(0xFF0051D5);
  
  // 辅助色
  static const Color secondaryColor = Color(0xFF5856D6);
  static const Color accentColor = Color(0xFFFF9500);
  static const Color successColor = Color(0xFF34C759);
  static const Color warningColor = Color(0xFFFF9500);
  static const Color errorColor = Color(0xFFFF3B30);
  static const Color infoColor = Color(0xFF5AC8FA);
  
  // 中性色
  static const Color backgroundColor = Color(0xFFF2F2F7);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);
  static const Color dividerColor = Color(0xFFE5E5EA);
  static const Color borderColor = Color(0xFFD1D1D6);
  
  // 文字颜色
  static const Color textPrimaryColor = Color(0xFF000000);
  static const Color textSecondaryColor = Color(0xFF8E8E93);
  static const Color textTertiaryColor = Color(0xFFC7C7CC);
  static const Color textDisabledColor = Color(0xFFAEAEB2);
  
  // 暗色主题颜色
  static const Color darkBackgroundColor = Color(0xFF000000);
  static const Color darkSurfaceColor = Color(0xFF1C1C1E);
  static const Color darkCardColor = Color(0xFF2C2C2E);
  static const Color darkDividerColor = Color(0xFF38383A);
  static const Color darkBorderColor = Color(0xFF48484A);
  
  static const Color darkTextPrimaryColor = Color(0xFFFFFFFF);
  static const Color darkTextSecondaryColor = Color(0xFF8E8E93);
  static const Color darkTextTertiaryColor = Color(0xFF48484A);
  
  // 渐变色
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryColor, primaryLightColor],
  );
  
  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF667eea),
      Color(0xFF764ba2),
    ],
  );
  
  static const LinearGradient goldGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFD700),
      Color(0xFFFFA500),
    ],
  );
  
  // 阴影
  static const BoxShadow defaultShadow = BoxShadow(
    color: Color(0x1A000000),
    blurRadius: 10,
    offset: Offset(0, 2),
  );
  
  static const BoxShadow cardShadow = BoxShadow(
    color: Color(0x0F000000),
    blurRadius: 20,
    offset: Offset(0, 4),
  );
  
  static const BoxShadow buttonShadow = BoxShadow(
    color: Color(0x1A000000),
    blurRadius: 8,
    offset: Offset(0, 2),
  );
  
  // 字体
  static const String fontFamily = 'SF Pro Display';
  static const String textFontFamily = 'SF Pro Text';
  
  // 亮色主题
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      cardColor: cardColor,
      dividerColor: dividerColor,
      
      // 字体主题
      fontFamily: fontFamily,
      textTheme: _lightTextTheme,
      
      // AppBar主题
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        titleTextStyle: TextStyle(
          color: textPrimaryColor,
          fontSize: 17,
          fontWeight: FontWeight.w600,
          fontFamily: fontFamily,
        ),
        iconTheme: IconThemeData(
          color: textPrimaryColor,
          size: 24,
        ),
      ),
      
      // 卡片主题
      cardTheme: CardTheme(
        color: cardColor,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        shadowColor: Colors.black.withOpacity(0.1),
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 16,
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: fontFamily,
          ),
        ),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        hintStyle: const TextStyle(
          color: textSecondaryColor,
          fontSize: 16,
          fontFamily: textFontFamily,
        ),
      ),
      
      // 底部导航栏主题
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: surfaceColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: textSecondaryColor,
        type: BottomNavigationBarType.fixed,
        elevation: 0,
        selectedLabelStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          fontFamily: textFontFamily,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          fontFamily: textFontFamily,
        ),
      ),
      
      // 对话框主题
      dialogTheme: DialogTheme(
        backgroundColor: surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        titleTextStyle: const TextStyle(
          color: textPrimaryColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
          fontFamily: fontFamily,
        ),
        contentTextStyle: const TextStyle(
          color: textSecondaryColor,
          fontSize: 16,
          fontFamily: textFontFamily,
        ),
      ),
      
      // 分割线主题
      dividerTheme: const DividerThemeData(
        color: dividerColor,
        thickness: 0.5,
        space: 1,
      ),
      
      // 图标主题
      iconTheme: const IconThemeData(
        color: textPrimaryColor,
        size: 24,
      ),
      
      // 列表瓦片主题
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        titleTextStyle: TextStyle(
          color: textPrimaryColor,
          fontSize: 16,
          fontWeight: FontWeight.w500,
          fontFamily: textFontFamily,
        ),
        subtitleTextStyle: TextStyle(
          color: textSecondaryColor,
          fontSize: 14,
          fontFamily: textFontFamily,
        ),
      ),
    );
  }
  
  // 暗色主题
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primarySwatch: _createMaterialColor(primaryColor),
      primaryColor: primaryColor,
      scaffoldBackgroundColor: darkBackgroundColor,
      cardColor: darkCardColor,
      dividerColor: darkDividerColor,
      
      // 字体主题
      fontFamily: fontFamily,
      textTheme: _darkTextTheme,
      
      // AppBar主题
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        titleTextStyle: TextStyle(
          color: darkTextPrimaryColor,
          fontSize: 17,
          fontWeight: FontWeight.w600,
          fontFamily: fontFamily,
        ),
        iconTheme: IconThemeData(
          color: darkTextPrimaryColor,
          size: 24,
        ),
      ),
      
      // 其他主题配置与亮色主题类似，但使用暗色调色板
      cardTheme: CardTheme(
        color: darkCardColor,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 16,
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            fontFamily: fontFamily,
          ),
        ),
      ),
      
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkSurfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: darkBorderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: darkBorderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        hintStyle: const TextStyle(
          color: darkTextSecondaryColor,
          fontSize: 16,
          fontFamily: textFontFamily,
        ),
      ),
    );
  }
  
  // 亮色文字主题
  static const TextTheme _lightTextTheme = TextTheme(
    displayLarge: TextStyle(
      color: textPrimaryColor,
      fontSize: 32,
      fontWeight: FontWeight.bold,
      fontFamily: fontFamily,
    ),
    displayMedium: TextStyle(
      color: textPrimaryColor,
      fontSize: 28,
      fontWeight: FontWeight.bold,
      fontFamily: fontFamily,
    ),
    displaySmall: TextStyle(
      color: textPrimaryColor,
      fontSize: 24,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
    ),
    headlineLarge: TextStyle(
      color: textPrimaryColor,
      fontSize: 22,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
    ),
    headlineMedium: TextStyle(
      color: textPrimaryColor,
      fontSize: 20,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
    ),
    headlineSmall: TextStyle(
      color: textPrimaryColor,
      fontSize: 18,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
    ),
    titleLarge: TextStyle(
      color: textPrimaryColor,
      fontSize: 17,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
    ),
    titleMedium: TextStyle(
      color: textPrimaryColor,
      fontSize: 16,
      fontWeight: FontWeight.w500,
      fontFamily: textFontFamily,
    ),
    titleSmall: TextStyle(
      color: textPrimaryColor,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      fontFamily: textFontFamily,
    ),
    bodyLarge: TextStyle(
      color: textPrimaryColor,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      fontFamily: textFontFamily,
    ),
    bodyMedium: TextStyle(
      color: textPrimaryColor,
      fontSize: 14,
      fontWeight: FontWeight.w400,
      fontFamily: textFontFamily,
    ),
    bodySmall: TextStyle(
      color: textSecondaryColor,
      fontSize: 12,
      fontWeight: FontWeight.w400,
      fontFamily: textFontFamily,
    ),
    labelLarge: TextStyle(
      color: textPrimaryColor,
      fontSize: 16,
      fontWeight: FontWeight.w500,
      fontFamily: textFontFamily,
    ),
    labelMedium: TextStyle(
      color: textSecondaryColor,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      fontFamily: textFontFamily,
    ),
    labelSmall: TextStyle(
      color: textTertiaryColor,
      fontSize: 12,
      fontWeight: FontWeight.w500,
      fontFamily: textFontFamily,
    ),
  );
  
  // 暗色文字主题
  static const TextTheme _darkTextTheme = TextTheme(
    displayLarge: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 32,
      fontWeight: FontWeight.bold,
      fontFamily: fontFamily,
    ),
    displayMedium: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 28,
      fontWeight: FontWeight.bold,
      fontFamily: fontFamily,
    ),
    displaySmall: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 24,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
    ),
    headlineLarge: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 22,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
    ),
    headlineMedium: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 20,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
    ),
    headlineSmall: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 18,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
    ),
    titleLarge: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 17,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
    ),
    titleMedium: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 16,
      fontWeight: FontWeight.w500,
      fontFamily: textFontFamily,
    ),
    titleSmall: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      fontFamily: textFontFamily,
    ),
    bodyLarge: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      fontFamily: textFontFamily,
    ),
    bodyMedium: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 14,
      fontWeight: FontWeight.w400,
      fontFamily: textFontFamily,
    ),
    bodySmall: TextStyle(
      color: darkTextSecondaryColor,
      fontSize: 12,
      fontWeight: FontWeight.w400,
      fontFamily: textFontFamily,
    ),
    labelLarge: TextStyle(
      color: darkTextPrimaryColor,
      fontSize: 16,
      fontWeight: FontWeight.w500,
      fontFamily: textFontFamily,
    ),
    labelMedium: TextStyle(
      color: darkTextSecondaryColor,
      fontSize: 14,
      fontWeight: FontWeight.w500,
      fontFamily: textFontFamily,
    ),
    labelSmall: TextStyle(
      color: darkTextTertiaryColor,
      fontSize: 12,
      fontWeight: FontWeight.w500,
      fontFamily: textFontFamily,
    ),
  );
  
  // 创建Material颜色
  static MaterialColor _createMaterialColor(Color color) {
    List strengths = <double>[.05];
    Map<int, Color> swatch = {};
    final int r = color.red, g = color.green, b = color.blue;

    for (int i = 1; i < 10; i++) {
      strengths.add(0.1 * i);
    }
    for (var strength in strengths) {
      final double ds = 0.5 - strength;
      swatch[(strength * 1000).round()] = Color.fromRGBO(
        r + ((ds < 0 ? r : (255 - r)) * ds).round(),
        g + ((ds < 0 ? g : (255 - g)) * ds).round(),
        b + ((ds < 0 ? b : (255 - b)) * ds).round(),
        1,
      );
    }
    return MaterialColor(color.value, swatch);
  }
}
