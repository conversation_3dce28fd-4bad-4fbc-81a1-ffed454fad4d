import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity('kyc_documents')
export class KycDocument {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'document_type', length: 50 })
  documentType: string;

  @Column({ name: 'document_number', length: 100 })
  documentNumber: string;

  @Column({ name: 'document_url', nullable: true })
  documentUrl: string;

  @Column({ name: 'verified_at', type: 'timestamp', nullable: true })
  verifiedAt: Date;

  @Column({ name: 'verified_by', nullable: true })
  verifiedBy: string;

  @Column({ name: 'rejection_reason', nullable: true })
  rejectionReason: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.kycDocuments, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  // 方法
  verify(verifiedBy: string): void {
    this.verifiedAt = new Date();
    this.verifiedBy = verifiedBy;
    this.rejectionReason = null;
  }

  reject(reason: string): void {
    this.rejectionReason = reason;
    this.verifiedAt = null;
    this.verifiedBy = null;
  }

  get isVerified(): boolean {
    return !!this.verifiedAt;
  }
}
