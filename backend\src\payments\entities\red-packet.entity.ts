import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { RedPacketClaim } from './red-packet-claim.entity';

@Entity('red_packets')
export class RedPacket {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'sender_id' })
  senderId: string;

  @Column({
    name: 'total_amount',
    type: 'decimal',
    precision: 15,
    scale: 2,
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  totalAmount: number;

  @Column({ name: 'total_count' })
  totalCount: number;

  @Column({
    name: 'remaining_amount',
    type: 'decimal',
    precision: 15,
    scale: 2,
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  remainingAmount: number;

  @Column({ name: 'remaining_count' })
  remainingCount: number;

  @Column({ length: 10, default: 'CNY' })
  currency: string;

  @Column({ nullable: true })
  message: string;

  @Column({ name: 'expires_at', type: 'timestamp' })
  expiresAt: Date;

  @Column({ name: 'is_random', default: true })
  isRandom: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'sender_id' })
  sender: User;

  @OneToMany(() => RedPacketClaim, (claim) => claim.redPacket)
  claims: RedPacketClaim[];

  // 虚拟属性
  get isExpired(): boolean {
    return this.expiresAt < new Date();
  }

  get isFinished(): boolean {
    return this.remainingCount <= 0 || this.remainingAmount <= 0;
  }

  get isAvailable(): boolean {
    return !this.isExpired && !this.isFinished;
  }

  get claimedAmount(): number {
    return this.totalAmount - this.remainingAmount;
  }

  get claimedCount(): number {
    return this.totalCount - this.remainingCount;
  }

  get averageAmount(): number {
    if (this.totalCount === 0) return 0;
    return this.totalAmount / this.totalCount;
  }

  // 方法
  /**
   * 计算红包金额（随机红包算法）
   */
  calculateAmount(): number {
    if (!this.isAvailable) {
      throw new Error('红包不可用');
    }

    if (this.remainingCount === 1) {
      // 最后一个红包，返回剩余金额
      return this.remainingAmount;
    }

    if (!this.isRandom) {
      // 平均红包
      return Math.round((this.remainingAmount / this.remainingCount) * 100) / 100;
    }

    // 随机红包算法：确保每个红包至少0.01元
    const minAmount = 0.01;
    const maxAmount = (this.remainingAmount - (this.remainingCount - 1) * minAmount) * 2 / this.remainingCount;
    
    const amount = Math.random() * maxAmount;
    return Math.max(minAmount, Math.round(amount * 100) / 100);
  }

  /**
   * 领取红包
   */
  claim(amount: number): void {
    if (!this.isAvailable) {
      throw new Error('红包不可用');
    }

    if (amount > this.remainingAmount) {
      throw new Error('红包余额不足');
    }

    this.remainingAmount -= amount;
    this.remainingCount -= 1;
  }

  /**
   * 检查用户是否可以领取
   */
  canClaimBy(userId: string): boolean {
    if (!this.isAvailable) {
      return false;
    }

    // 不能领取自己发的红包
    if (this.senderId === userId) {
      return false;
    }

    return true;
  }

  /**
   * 获取红包状态
   */
  getStatus(): 'AVAILABLE' | 'EXPIRED' | 'FINISHED' {
    if (this.isExpired) {
      return 'EXPIRED';
    }
    if (this.isFinished) {
      return 'FINISHED';
    }
    return 'AVAILABLE';
  }

  /**
   * 获取红包摘要
   */
  getSummary(): {
    id: string;
    totalAmount: number;
    totalCount: number;
    remainingAmount: number;
    remainingCount: number;
    claimedAmount: number;
    claimedCount: number;
    currency: string;
    message: string;
    status: string;
    isRandom: boolean;
    createdAt: Date;
    expiresAt: Date;
  } {
    return {
      id: this.id,
      totalAmount: this.totalAmount,
      totalCount: this.totalCount,
      remainingAmount: this.remainingAmount,
      remainingCount: this.remainingCount,
      claimedAmount: this.claimedAmount,
      claimedCount: this.claimedCount,
      currency: this.currency,
      message: this.message,
      status: this.getStatus(),
      isRandom: this.isRandom,
      createdAt: this.createdAt,
      expiresAt: this.expiresAt,
    };
  }
}
