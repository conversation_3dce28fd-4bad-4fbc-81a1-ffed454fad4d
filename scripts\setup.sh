#!/bin/bash

# 钱包应用项目初始化脚本
# 用于快速搭建开发环境

set -e

echo "🚀 开始初始化钱包应用项目..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查必要的工具是否已安装
check_prerequisites() {
    print_step "检查开发环境依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js (推荐版本 18+)"
        exit 1
    fi
    print_message "✓ Node.js 已安装: $(node --version)"
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装"
        exit 1
    fi
    print_message "✓ npm 已安装: $(npm --version)"
    
    # 检查 Flutter
    if ! command -v flutter &> /dev/null; then
        print_warning "Flutter 未安装，请访问 https://flutter.dev/docs/get-started/install 安装"
        print_warning "如果只开发后端，可以跳过 Flutter 安装"
    else
        print_message "✓ Flutter 已安装: $(flutter --version | head -n 1)"
    fi
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        print_warning "Docker 未安装，请访问 https://docs.docker.com/get-docker/ 安装"
        print_warning "Docker 用于本地开发环境，可选安装"
    else
        print_message "✓ Docker 已安装: $(docker --version)"
    fi
    
    # 检查 Git
    if ! command -v git &> /dev/null; then
        print_error "Git 未安装，请先安装 Git"
        exit 1
    fi
    print_message "✓ Git 已安装: $(git --version)"
}

# 创建项目目录结构
create_project_structure() {
    print_step "创建项目目录结构..."
    
    # 前端目录
    mkdir -p frontend/{lib/{core,data,domain,presentation},android,ios,web,test}
    mkdir -p frontend/lib/core/{constants,errors,network,storage,utils,theme}
    mkdir -p frontend/lib/data/{datasources/{local,remote},models,repositories}
    mkdir -p frontend/lib/domain/{entities,repositories,usecases}
    mkdir -p frontend/lib/presentation/{pages,widgets,providers,routes}
    
    # 后端目录
    mkdir -p backend/{src,test,docker}
    mkdir -p backend/src/{auth,payment,user,wallet,notification,common}
    mkdir -p backend/src/common/{decorators,filters,guards,interceptors,pipes}
    
    # 数据库目录
    mkdir -p database/{migrations,seeds,scripts}
    
    # 文档目录
    mkdir -p docs/{api,design,deployment}
    
    # 配置目录
    mkdir -p config/{development,staging,production}
    
    # 脚本目录
    mkdir -p scripts/{build,deploy,test}
    
    # Kubernetes配置
    mkdir -p k8s/{development,staging,production}
    
    # 监控配置
    mkdir -p monitoring/{prometheus,grafana/{dashboards,datasources}}
    
    # Nginx配置
    mkdir -p nginx/{conf.d,ssl}
    
    # 测试目录
    mkdir -p tests/{unit,integration,e2e}
    
    print_message "✓ 项目目录结构创建完成"
}

# 初始化 Git 仓库
init_git_repo() {
    print_step "初始化 Git 仓库..."
    
    if [ ! -d ".git" ]; then
        git init
        print_message "✓ Git 仓库初始化完成"
    else
        print_message "✓ Git 仓库已存在"
    fi
    
    # 创建初始提交
    if [ -z "$(git log --oneline 2>/dev/null)" ]; then
        git add .
        git commit -m "feat: 初始化钱包应用项目结构"
        print_message "✓ 创建初始提交"
    fi
}

# 安装项目依赖
install_dependencies() {
    print_step "安装项目依赖..."
    
    # 安装根目录依赖
    print_message "安装根目录依赖..."
    npm install
    
    # 安装后端依赖
    if [ -d "backend" ]; then
        print_message "安装后端依赖..."
        cd backend
        if [ ! -f "package.json" ]; then
            npm init -y
            npm install @nestjs/core @nestjs/common @nestjs/platform-express
            npm install --save-dev @nestjs/cli typescript @types/node
        else
            npm install
        fi
        cd ..
    fi
    
    # 安装前端依赖
    if [ -d "frontend" ] && command -v flutter &> /dev/null; then
        print_message "安装前端依赖..."
        cd frontend
        if [ ! -f "pubspec.yaml" ]; then
            flutter create . --org com.wallet.app
        fi
        flutter pub get
        cd ..
    fi
    
    print_message "✓ 依赖安装完成"
}

# 创建环境配置文件
create_env_files() {
    print_step "创建环境配置文件..."
    
    # 后端环境配置
    if [ ! -f "backend/.env.example" ]; then
        cat > backend/.env.example << EOF
# 数据库配置
DATABASE_URL=postgresql://wallet_user:wallet_pass@localhost:5432/wallet_db
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# 加密配置
ENCRYPTION_KEY=your-32-char-encryption-key-here

# 应用配置
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# 第三方服务
SMS_API_KEY=your-sms-api-key
EMAIL_API_KEY=your-email-api-key
PAYMENT_GATEWAY_KEY=your-payment-gateway-key

# 文件存储
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=wallet-app-files

# 监控配置
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=debug
EOF
    fi
    
    # 复制开发环境配置
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_message "✓ 创建后端环境配置文件"
    fi
    
    # 前端环境配置
    if [ ! -f "frontend/.env" ]; then
        cat > frontend/.env << EOF
# API配置
API_BASE_URL=http://localhost:3000/api/v1
WS_BASE_URL=ws://localhost:3000

# 应用配置
APP_NAME=Wallet App
APP_VERSION=1.0.0

# 第三方服务
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
FIREBASE_API_KEY=your-firebase-api-key
EOF
        print_message "✓ 创建前端环境配置文件"
    fi
}

# 设置开发工具
setup_dev_tools() {
    print_step "设置开发工具..."
    
    # 安装 Husky (Git hooks)
    if command -v npm &> /dev/null; then
        npx husky install
        print_message "✓ Husky Git hooks 设置完成"
    fi
    
    # 创建 VS Code 配置
    mkdir -p .vscode
    if [ ! -f ".vscode/settings.json" ]; then
        cat > .vscode/settings.json << EOF
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "dart.flutterSdkPath": null,
  "dart.lineLength": 80,
  "files.associations": {
    "*.dart": "dart"
  },
  "emmet.includeLanguages": {
    "dart": "html"
  }
}
EOF
        print_message "✓ VS Code 配置创建完成"
    fi
    
    # 创建推荐扩展配置
    if [ ! -f ".vscode/extensions.json" ]; then
        cat > .vscode/extensions.json << EOF
{
  "recommendations": [
    "dart-code.dart-code",
    "dart-code.flutter",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode-remote.remote-containers"
  ]
}
EOF
        print_message "✓ VS Code 扩展推荐配置创建完成"
    fi
}

# 创建 README 文件
create_readme() {
    if [ ! -f "README.md" ]; then
        print_step "README.md 已存在，跳过创建"
    else
        print_message "✓ README.md 已存在"
    fi
}

# 验证安装
verify_installation() {
    print_step "验证安装..."
    
    # 检查文件是否存在
    local files=(
        "package.json"
        "docker-compose.yml"
        ".gitignore"
        "backend/.env"
        "frontend/.env"
    )
    
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            print_message "✓ $file 存在"
        else
            print_warning "✗ $file 不存在"
        fi
    done
    
    # 检查目录是否存在
    local dirs=(
        "frontend/lib"
        "backend/src"
        "docs"
        "scripts"
        "k8s"
    )
    
    for dir in "${dirs[@]}"; do
        if [ -d "$dir" ]; then
            print_message "✓ $dir/ 目录存在"
        else
            print_warning "✗ $dir/ 目录不存在"
        fi
    done
}

# 显示下一步操作
show_next_steps() {
    print_step "安装完成！下一步操作："
    echo ""
    echo "1. 配置环境变量："
    echo "   - 编辑 backend/.env 文件，设置数据库和第三方服务配置"
    echo "   - 编辑 frontend/.env 文件，设置API地址和第三方服务配置"
    echo ""
    echo "2. 启动开发环境："
    echo "   - 启动数据库: docker-compose up -d postgres redis"
    echo "   - 启动后端: cd backend && npm run start:dev"
    echo "   - 启动前端: cd frontend && flutter run"
    echo ""
    echo "3. 开发工具："
    echo "   - API文档: http://localhost:3000/api/docs"
    echo "   - 数据库管理: 使用 pgAdmin 或 DBeaver"
    echo "   - 监控面板: http://localhost:3001 (Grafana)"
    echo ""
    echo "4. 有用的命令："
    echo "   - npm run dev: 同时启动前后端开发服务器"
    echo "   - npm run test: 运行所有测试"
    echo "   - npm run build: 构建生产版本"
    echo "   - npm run docker:up: 启动完整的Docker开发环境"
    echo ""
    print_message "🎉 项目初始化完成！开始愉快的开发吧！"
}

# 主函数
main() {
    echo "========================================"
    echo "    钱包应用项目初始化脚本 v1.0"
    echo "========================================"
    echo ""
    
    check_prerequisites
    create_project_structure
    init_git_repo
    install_dependencies
    create_env_files
    setup_dev_tools
    create_readme
    verify_installation
    show_next_steps
}

# 运行主函数
main "$@"
