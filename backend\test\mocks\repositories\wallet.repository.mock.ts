import { BaseMockRepository } from './base.repository.mock';
import { Wallet, WalletFactory, WalletType } from '../../fixtures/wallets/wallet.factory';

/**
 * 模拟钱包仓库
 * 继承基础模拟仓库，实现钱包特定方法
 */
export class WalletMockRepository extends BaseMockRepository<Wallet> {
  /**
   * 查找用户的所有钱包
   * @param userId 用户ID
   * @returns 钱包数组
   */
  findByUserId(userId: string): Promise<Wallet[]> {
    return this.findBy({ userId } as Partial<Wallet>);
  }

  /**
   * 查找用户的主钱包
   * @param userId 用户ID
   * @returns 主钱包或null
   */
  findPrimaryWallet(userId: string): Promise<Wallet | null> {
    return this.findOne({
      userId,
      type: WalletType.PRIMARY
    } as Partial<Wallet>);
  }

  /**
   * 更新钱包余额
   * @param walletId 钱包ID
   * @param amount 金额变动，正数为增加，负数为减少
   * @returns 更新后的钱包
   */
  async updateBalance(walletId: string, amount: number): Promise<Wallet | null> {
    const wallet = await this.findOneById(walletId);
    if (!wallet) return null;

    const newBalance = wallet.balance + amount;
    if (newBalance < 0) {
      throw new Error('Insufficient balance');
    }

    return this.save({
      ...wallet,
      balance: newBalance,
      updatedAt: new Date()
    });
  }

  /**
   * 初始化测试数据
   * @param userIds 用户ID数组
   */
  seed(userIds: string[]): void {
    const wallets: Wallet[] = [];

    // 为每个用户创建主钱包
    userIds.forEach(userId => {
      wallets.push(WalletFactory.create({ userId, type: WalletType.PRIMARY }));

      // 随机为部分用户创建额外钱包
      if (Math.random() > 0.5) {
        wallets.push(WalletFactory.createWithType(WalletType.SAVINGS, { userId }));
      }

      if (Math.random() > 0.7) {
        wallets.push(WalletFactory.createWithType(WalletType.INVESTMENT, { userId }));
      }
    });

    this.entities = wallets;
  }

  /**
   * 查找所有活跃钱包
   * @returns 活跃钱包列表
   */
  findActiveWallets(): Promise<Wallet[]> {
    return this.findBy({ status: 'active' } as Partial<Wallet>);
  }

  /**
   * 查找特定类型的钱包
   * @param type 钱包类型
   * @returns 钱包列表
   */
  findByType(type: WalletType): Promise<Wallet[]> {
    return this.findBy({ type } as Partial<Wallet>);
  }

  /**
   * 获取钱包总余额
   * @param userId 用户ID
   * @returns 总余额
   */
  async getTotalBalance(userId: string): Promise<number> {
    const wallets = await this.findByUserId(userId);
    return wallets.reduce((total, wallet) => total + wallet.balance, 0);
  }
}

/**
 * 创建预填充的钱包仓库
 * @param initialWallets 初始钱包数组
 * @returns 钱包仓库实例
 */
export function createWalletRepository(initialWallets: Wallet[] = []): WalletMockRepository {
  const repo = new WalletMockRepository();

  if (initialWallets.length > 0) {
    repo.initialize(initialWallets);
  }

  return repo;
}
