/**
 * 通知对象接口
 */
export interface Notification {
  /**
   * 接收通知的用户ID
   */
  userId: string;

  /**
   * 通知类型
   */
  type: string;

  /**
   * 通知标题
   */
  title: string;

  /**
   * 通知内容
   */
  content: string;

  /**
   * 附加数据
   */
  data?: Record<string, any>;
}

/**
 * 通知服务接口
 * 定义各种通知发送方法
 */
export interface INotificationService {
  /**
   * 发送单个通知
   * @param notification 通知对象
   */
  sendNotification(notification: Notification): Promise<void>;

  /**
   * 批量发送通知
   * @param notifications 通知对象数组
   */
  sendBatchNotifications(notifications: Notification[]): Promise<void>;

  /**
   * 发送电子邮件
   * @param to 收件人邮箱
   * @param subject 邮件主题
   * @param body 邮件内容
   */
  sendEmail(to: string, subject: string, body: string): Promise<void>;

  /**
   * 发送短信
   * @param phoneNumber 手机号
   * @param message 短信内容
   */
  sendSms(phoneNumber: string, message: string): Promise<void>;

  /**
   * 发送推送通知
   * @param userId 用户ID
   * @param title 通知标题
   * @param body 通知内容
   * @param data 附加数据
   */
  sendPushNotification(userId: string, title: string, body: string, data?: any): Promise<void>;
}
