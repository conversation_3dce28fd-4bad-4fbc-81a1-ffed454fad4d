/**
 * 加密服务接口
 * 定义加密、哈希、签名等方法
 */
export interface ICryptoService {
  /**
   * 哈希密码
   * @param password 明文密码
   * @returns 哈希后的密码
   */
  hashPassword(password: string): Promise<string>;

  /**
   * 验证密码
   * @param plainPassword 明文密码
   * @param hashedPassword 哈希后的密码
   * @returns 是否匹配
   */
  verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean>;

  /**
   * 生成随机令牌
   * @param length 令牌长度
   * @returns 随机令牌
   */
  generateRandomToken(length?: number): string;

  /**
   * 加密数据
   * @param data 待加密数据
   * @param key 加密密钥(可选，默认使用系统密钥)
   * @returns 加密后的数据
   */
  encrypt(data: string, key?: string): Promise<string>;

  /**
   * 解密数据
   * @param encryptedData 加密后的数据
   * @param key 解密密钥(可选，默认使用系统密钥)
   * @returns 解密后的数据
   */
  decrypt(encryptedData: string, key?: string): Promise<string>;

  /**
   * 生成签名
   * @param data 待签名数据
   * @param privateKey 私钥(可选，默认使用系统私钥)
   * @returns 签名
   */
  sign(data: string, privateKey?: string): Promise<string>;

  /**
   * 验证签名
   * @param data 原始数据
   * @param signature 签名
   * @param publicKey 公钥(可选，默认使用系统公钥)
   * @returns 签名是否有效
   */
  verify(data: string, signature: string, publicKey?: string): Promise<boolean>;
}
