import { ApiProperty } from '@nestjs/swagger';
import {
  IsNumber,
  IsOptional,
  IsString,
  IsBoolean,
  IsPositive,
  Min,
  Max,
  Length,
} from 'class-validator';

export class CreateRedPacketDto {
  @ApiProperty({
    description: '红包总金额',
    example: 100.00,
  })
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsPositive()
  @Max(200)
  totalAmount: number;

  @ApiProperty({
    description: '红包个数',
    example: 10,
  })
  @IsNumber()
  @Min(1)
  @Max(100)
  totalCount: number;

  @ApiProperty({
    description: '货币类型',
    example: 'CNY',
    default: 'CNY',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(3, 10)
  currency?: string;

  @ApiProperty({
    description: '红包祝福语',
    example: '恭喜发财，大吉大利！',
    required: false,
  })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  message?: string;

  @ApiProperty({
    description: '过期时间（秒）',
    example: 86400,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(3600) // 最少1小时
  @Max(604800) // 最长7天
  expiresIn?: number;

  @ApiProperty({
    description: '是否为随机红包',
    example: true,
    default: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isRandom?: boolean;
}
