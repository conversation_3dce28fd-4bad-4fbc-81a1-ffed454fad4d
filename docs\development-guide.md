# 钱包应用开发指南

## 快速开始

### 环境要求

- **Flutter**: 3.10.0 或更高版本
- **Dart**: 3.0.0 或更高版本
- **Node.js**: 18.0.0 或更高版本
- **PostgreSQL**: 15.0 或更高版本
- **Redis**: 7.0 或更高版本
- **Docker**: 20.10 或更高版本（可选）

### 项目初始化

1. **克隆项目并初始化**
```bash
git clone <repository-url>
cd wallet-app
chmod +x scripts/setup.sh
./scripts/setup.sh
```

2. **配置环境变量**
```bash
# 编辑后端环境配置
cp backend/.env.example backend/.env
# 根据实际情况修改配置

# 编辑前端环境配置
cp frontend/.env.example frontend/.env
# 根据实际情况修改配置
```

3. **启动开发环境**
```bash
# 启动数据库服务
docker-compose up -d postgres redis

# 启动后端服务
cd backend
npm run start:dev

# 启动前端应用
cd frontend
flutter run
```

## 项目结构详解

### 前端结构 (Flutter)

```
frontend/
├── lib/
│   ├── core/                    # 核心功能
│   │   ├── constants/           # 常量定义
│   │   ├── theme/              # 主题配置
│   │   ├── network/            # 网络层
│   │   ├── storage/            # 本地存储
│   │   └── utils/              # 工具类
│   ├── data/                   # 数据层
│   │   ├── datasources/        # 数据源
│   │   ├── models/             # 数据模型
│   │   └── repositories/       # 仓库实现
│   ├── domain/                 # 业务层
│   │   ├── entities/           # 实体
│   │   ├── repositories/       # 仓库接口
│   │   └── usecases/           # 用例
│   └── presentation/           # 表现层
│       ├── pages/              # 页面
│       ├── widgets/            # 组件
│       ├── providers/          # 状态管理
│       └── routes/             # 路由配置
├── assets/                     # 资源文件
│   ├── images/                 # 图片
│   ├── icons/                  # 图标
│   ├── animations/             # 动画
│   └── fonts/                  # 字体
└── test/                       # 测试文件
```

### 后端结构 (Node.js + NestJS)

```
backend/
├── src/
│   ├── auth/                   # 认证模块
│   ├── user/                   # 用户模块
│   ├── wallet/                 # 钱包模块
│   ├── payment/                # 支付模块
│   ├── notification/           # 通知模块
│   └── common/                 # 公共模块
├── database/                   # 数据库相关
│   ├── migrations/             # 数据库迁移
│   └── seeds/                  # 种子数据
├── test/                       # 测试文件
└── docker/                     # Docker配置
```

## 核心功能实现

### 1. 用户认证系统

#### 注册流程
```dart
// 前端 - 用户注册
class RegisterUseCase {
  Future<AuthResult> call(RegisterParams params) async {
    // 1. 验证输入参数
    if (!_validatePhone(params.phone)) {
      throw InvalidPhoneException();
    }
    
    // 2. 发送短信验证码
    await _smsService.sendCode(params.phone);
    
    // 3. 验证短信验证码
    final isValid = await _smsService.verifyCode(
      params.phone, 
      params.smsCode
    );
    
    if (!isValid) {
      throw InvalidSmsCodeException();
    }
    
    // 4. 创建用户账户
    final user = await _authRepository.register(params);
    
    // 5. 保存用户信息
    await _storageService.saveUser(user);
    
    return AuthResult.success(user);
  }
}
```

#### 生物识别认证
```dart
// 生物识别认证组件
class BiometricAuth {
  static Future<bool> authenticate() async {
    final localAuth = LocalAuthentication();
    
    // 检查设备是否支持生物识别
    final isAvailable = await localAuth.canCheckBiometrics;
    if (!isAvailable) return false;
    
    // 获取可用的生物识别类型
    final availableBiometrics = await localAuth.getAvailableBiometrics();
    
    if (availableBiometrics.isEmpty) return false;
    
    // 执行生物识别认证
    try {
      final isAuthenticated = await localAuth.authenticate(
        localizedReason: '请验证您的身份以继续',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
      
      return isAuthenticated;
    } catch (e) {
      return false;
    }
  }
}
```

### 2. 支付功能实现

#### 扫码支付
```dart
// 扫码支付页面
class QRScanPage extends ConsumerStatefulWidget {
  @override
  ConsumerState<QRScanPage> createState() => _QRScanPageState();
}

class _QRScanPageState extends ConsumerState<QRScanPage> {
  QRViewController? controller;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const GlassmorphismAppBar(
        title: Text('扫一扫'),
      ),
      body: Stack(
        children: [
          // 相机预览
          QRView(
            key: GlobalKey(debugLabel: 'QR'),
            onQRViewCreated: _onQRViewCreated,
            overlay: QrScannerOverlayShape(
              borderColor: AppTheme.primaryColor,
              borderRadius: 16,
              borderLength: 30,
              borderWidth: 4,
              cutOutSize: 250,
            ),
          ),
          
          // 扫描提示
          Positioned(
            bottom: 100,
            left: 0,
            right: 0,
            child: Center(
              child: GlassmorphismCard(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                child: const Text(
                  '将二维码放入框内，即可自动扫描',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      _handleQRCode(scanData.code);
    });
  }
  
  void _handleQRCode(String? code) {
    if (code != null) {
      // 解析二维码内容
      final paymentInfo = PaymentQRCode.parse(code);
      
      // 跳转到支付确认页面
      context.push('/payment/confirm', extra: paymentInfo);
    }
  }
}
```

#### 转账功能
```dart
// 转账用例
class TransferUseCase {
  Future<TransferResult> call(TransferParams params) async {
    // 1. 验证转账参数
    _validateTransferParams(params);
    
    // 2. 检查余额
    final wallet = await _walletRepository.getWallet(params.fromUserId);
    if (wallet.balance < params.amount) {
      throw InsufficientBalanceException();
    }
    
    // 3. 检查转账限额
    final dailyAmount = await _transactionRepository.getDailyTransferAmount(
      params.fromUserId,
    );
    
    if (dailyAmount + params.amount > AppConstants.maxDailyTransferAmount) {
      throw DailyLimitExceededException();
    }
    
    // 4. 创建转账交易
    final transaction = await _paymentRepository.createTransfer(params);
    
    // 5. 发送通知
    await _notificationService.sendTransferNotification(transaction);
    
    return TransferResult.success(transaction);
  }
}
```

### 3. 液态毛玻璃UI实现

#### 钱包卡片组件
```dart
// 钱包余额卡片
class WalletBalanceCard extends StatelessWidget {
  final Wallet wallet;
  final VoidCallback? onTap;
  
  const WalletBalanceCard({
    super.key,
    required this.wallet,
    this.onTap,
  });
  
  @override
  Widget build(BuildContext context) {
    return GlassmorphismCard(
      onTap: onTap,
      gradient: AppTheme.cardGradient,
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      boxShadow: [AppTheme.cardShadow],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 余额标题
          Text(
            '余额',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // 余额金额
          Row(
            children: [
              Text(
                AppConstants.cnySymbol,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                wallet.balance.toStringAsFixed(2),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 操作按钮
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  icon: CupertinoIcons.arrow_up_circle,
                  label: '转账',
                  onTap: () => context.push('/transfer'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  icon: CupertinoIcons.qrcode_viewfinder,
                  label: '收款',
                  onTap: () => context.push('/receive'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GlassmorphismButton(
      onPressed: onTap,
      backgroundColor: Colors.white.withOpacity(0.2),
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Column(
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
```

### 4. 状态管理 (Riverpod)

#### 用户状态管理
```dart
// 用户状态
@freezed
class UserState with _$UserState {
  const factory UserState({
    User? user,
    @Default(false) bool isLoading,
    @Default(false) bool isLoggedIn,
    String? error,
  }) = _UserState;
}

// 用户状态提供者
@riverpod
class UserNotifier extends _$UserNotifier {
  @override
  UserState build() {
    return const UserState();
  }
  
  Future<void> login(String phone, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final authResult = await ref.read(authRepositoryProvider).login(
        phone: phone,
        password: password,
      );
      
      if (authResult.isSuccess) {
        state = state.copyWith(
          user: authResult.user,
          isLoggedIn: true,
          isLoading: false,
        );
        
        // 保存登录状态
        await ref.read(storageServiceProvider).saveUser(authResult.user!);
      } else {
        state = state.copyWith(
          error: authResult.error,
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }
  
  Future<void> logout() async {
    await ref.read(storageServiceProvider).clearUser();
    state = const UserState();
  }
}
```

## 安全最佳实践

### 1. 数据加密
```dart
// 敏感数据加密存储
class SecureStorage {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
    ),
  );
  
  static Future<void> store(String key, String value) async {
    await _storage.write(key: key, value: value);
  }
  
  static Future<String?> read(String key) async {
    return await _storage.read(key: key);
  }
  
  static Future<void> delete(String key) async {
    await _storage.delete(key: key);
  }
  
  static Future<void> deleteAll() async {
    await _storage.deleteAll();
  }
}
```

### 2. 网络安全
```dart
// 网络请求拦截器
class AuthInterceptor extends Interceptor {
  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    // 添加认证头
    final token = await SecureStorage.read(AppConstants.accessTokenKey);
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    
    // 添加设备信息
    final deviceInfo = await DeviceInfoService.getDeviceInfo();
    options.headers['X-Device-ID'] = deviceInfo.deviceId;
    options.headers['X-App-Version'] = deviceInfo.appVersion;
    
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // 处理401错误，自动刷新token
    if (err.response?.statusCode == 401) {
      final refreshed = await _refreshToken();
      if (refreshed) {
        // 重试原请求
        final clonedRequest = await _retry(err.requestOptions);
        handler.resolve(clonedRequest);
        return;
      }
    }
    
    handler.next(err);
  }
}
```

## 测试策略

### 1. 单元测试
```dart
// 用户用例测试
void main() {
  group('LoginUseCase', () {
    late LoginUseCase loginUseCase;
    late MockAuthRepository mockAuthRepository;
    late MockStorageService mockStorageService;
    
    setUp(() {
      mockAuthRepository = MockAuthRepository();
      mockStorageService = MockStorageService();
      loginUseCase = LoginUseCase(
        authRepository: mockAuthRepository,
        storageService: mockStorageService,
      );
    });
    
    test('should return success when login with valid credentials', () async {
      // Arrange
      const phone = '13800138000';
      const password = 'password123';
      final user = User(id: '1', phone: phone);
      
      when(mockAuthRepository.login(phone: phone, password: password))
          .thenAnswer((_) async => AuthResult.success(user));
      
      // Act
      final result = await loginUseCase.call(
        LoginParams(phone: phone, password: password),
      );
      
      // Assert
      expect(result.isSuccess, true);
      expect(result.user, user);
      verify(mockStorageService.saveUser(user)).called(1);
    });
  });
}
```

### 2. 集成测试
```dart
// 登录流程集成测试
void main() {
  group('Login Flow Integration Test', () {
    testWidgets('should complete login flow successfully', (tester) async {
      // 启动应用
      await tester.pumpWidget(const MyApp());
      
      // 导航到登录页面
      await tester.tap(find.text('登录'));
      await tester.pumpAndSettle();
      
      // 输入手机号
      await tester.enterText(
        find.byKey(const Key('phone_field')),
        '13800138000',
      );
      
      // 输入密码
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'password123',
      );
      
      // 点击登录按钮
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle();
      
      // 验证登录成功
      expect(find.text('首页'), findsOneWidget);
    });
  });
}
```

## 部署指南

### 1. 移动端部署
```bash
# Android构建
flutter build apk --release
flutter build appbundle --release

# iOS构建
flutter build ios --release
```

### 2. 后端部署
```bash
# Docker构建
docker build -t wallet-backend .

# Kubernetes部署
kubectl apply -f k8s/production/
```

### 3. CI/CD配置
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter test
      
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      - run: flutter build apk --release
      
  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to production
        run: |
          # 部署脚本
```

这个开发指南提供了完整的项目开发流程，包括环境搭建、核心功能实现、安全实践、测试策略和部署指南。开发团队可以按照这个指南逐步实现钱包应用的各个功能模块。
