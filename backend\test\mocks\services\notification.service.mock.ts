/**
 * 通知接口
 */
export interface Notification {
  userId: string;
  type: string;
  title: string;
  content: string;
  data?: Record<string, any>;
}

/**
 * 通知服务接口
 */
export interface INotificationService {
  sendNotification(notification: Notification): Promise<void>;
  sendBatchNotifications(notifications: Notification[]): Promise<void>;
  sendEmail(to: string, subject: string, body: string): Promise<void>;
  sendSms(phoneNumber: string, message: string): Promise<void>;
  sendPushNotification(userId: string, title: string, body: string, data?: any): Promise<void>;
}

/**
 * 模拟通知服务
 * 用于在测试中替代实际的通知服务
 */
export const mockNotificationService: jest.Mocked<INotificationService> = {
  sendNotification: jest.fn().mockImplementation(notification => {
    return Promise.resolve();
  }),

  sendBatchNotifications: jest.fn().mockImplementation(notifications => {
    return Promise.resolve();
  }),

  sendEmail: jest.fn().mockImplementation((to, subject, body) => {
    return Promise.resolve();
  }),

  sendSms: jest.fn().mockImplementation((phoneNumber, message) => {
    return Promise.resolve();
  }),

  sendPushNotification: jest.fn().mockImplementation((userId, title, body, data) => {
    return Promise.resolve();
  })
};

/**
 * 带错误处理的模拟通知服务
 * 可用于测试错误情况
 */
export const mockNotificationServiceWithErrors: jest.Mocked<INotificationService> = {
  sendNotification: jest.fn().mockRejectedValue(new Error('Failed to send notification')),

  sendBatchNotifications: jest.fn().mockRejectedValue(new Error('Failed to send batch notifications')),

  sendEmail: jest.fn().mockRejectedValue(new Error('Failed to send email')),

  sendSms: jest.fn().mockRejectedValue(new Error('Failed to send SMS')),

  sendPushNotification: jest.fn().mockRejectedValue(new Error('Failed to send push notification'))
};

/**
 * 重置所有模拟函数
 */
export function resetNotificationMocks(): void {
  mockNotificationService.sendNotification.mockClear();
  mockNotificationService.sendBatchNotifications.mockClear();
  mockNotificationService.sendEmail.mockClear();
  mockNotificationService.sendSms.mockClear();
  mockNotificationService.sendPushNotification.mockClear();
}
