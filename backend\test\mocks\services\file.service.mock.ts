/**
 * 文件服务接口
 */
export interface IFileService {
  uploadFile(file: any, path: string): Promise<string>;
  deleteFile(path: string): Promise<void>;
  validateFileType(file: any, allowedTypes: string[]): boolean;
  validateFileSize(file: any, maxSize: number): boolean;
}

/**
 * 模拟文件服务
 * 用于在测试中替代实际的文件服务
 */
export const mockFileService: jest.Mocked<IFileService> = {
  uploadFile: jest.fn().mockImplementation((file, path) => {
    return Promise.resolve(`https://storage.example.com/${path}/file-${Date.now()}.jpg`);
  }),

  deleteFile: jest.fn().mockImplementation(path => {
    return Promise.resolve();
  }),

  validateFileType: jest.fn().mockImplementation((file, allowedTypes) => {
    return true;
  }),

  validateFileSize: jest.fn().mockImplementation((file, maxSize) => {
    return true;
  })
};

/**
 * 带错误处理的模拟文件服务
 * 可用于测试错误情况
 */
export const mockFileServiceWithErrors: jest.Mocked<IFileService> = {
  uploadFile: jest.fn().mockRejectedValue(new Error('Upload failed')),

  deleteFile: jest.fn().mockRejectedValue(new Error('Delete failed')),

  validateFileType: jest.fn().mockReturnValue(false),

  validateFileSize: jest.fn().mockReturnValue(false)
};

/**
 * 重置所有模拟函数
 */
export function resetFileMocks(): void {
  mockFileService.uploadFile.mockClear();
  mockFileService.deleteFile.mockClear();
  mockFileService.validateFileType.mockClear();
  mockFileService.validateFileSize.mockClear();
}
