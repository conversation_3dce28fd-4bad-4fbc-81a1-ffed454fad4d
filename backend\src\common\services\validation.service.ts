import { Injectable } from '@nestjs/common';

@Injectable()
export class ValidationService {
  /**
   * 验证手机号格式
   */
  isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }

  /**
   * 验证邮箱格式
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  }

  /**
   * 验证密码强度
   */
  isValidPassword(password: string): boolean {
    // 密码必须包含字母和数字，长度6-20位
    const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/;
    return passwordRegex.test(password);
  }

  /**
   * 验证身份证号
   */
  isValidIdCard(idCard: string): boolean {
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    return idCardRegex.test(idCard);
  }

  /**
   * 验证银行卡号
   */
  isValidBankCard(cardNumber: string): boolean {
    // 银行卡号通常是13-19位数字
    const cardRegex = /^\d{13,19}$/;
    if (!cardRegex.test(cardNumber)) {
      return false;
    }

    // Luhn算法验证
    return this.luhnCheck(cardNumber);
  }

  /**
   * Luhn算法验证银行卡号
   */
  private luhnCheck(cardNumber: string): boolean {
    let sum = 0;
    let alternate = false;

    for (let i = cardNumber.length - 1; i >= 0; i--) {
      let n = parseInt(cardNumber.charAt(i), 10);

      if (alternate) {
        n *= 2;
        if (n > 9) {
          n = (n % 10) + 1;
        }
      }

      sum += n;
      alternate = !alternate;
    }

    return sum % 10 === 0;
  }

  /**
   * 验证金额格式
   */
  isValidAmount(amount: number): boolean {
    return amount > 0 && Number.isFinite(amount) && amount <= *********.99;
  }

  /**
   * 验证转账金额
   */
  isValidTransferAmount(amount: number, minAmount: number = 0.01, maxAmount: number = 50000): boolean {
    return this.isValidAmount(amount) && amount >= minAmount && amount <= maxAmount;
  }

  /**
   * 验证用户名
   */
  isValidUsername(username: string): boolean {
    // 用户名只能包含字母、数字、下划线，长度3-20位
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
  }

  /**
   * 验证真实姓名
   */
  isValidRealName(name: string): boolean {
    // 中文姓名2-10个字符，或英文姓名2-50个字符
    const chineseNameRegex = /^[\u4e00-\u9fa5]{2,10}$/;
    const englishNameRegex = /^[a-zA-Z\s]{2,50}$/;
    
    return chineseNameRegex.test(name) || englishNameRegex.test(name);
  }

  /**
   * 验证验证码格式
   */
  isValidVerificationCode(code: string, length: number = 6): boolean {
    const codeRegex = new RegExp(`^\\d{${length}}$`);
    return codeRegex.test(code);
  }

  /**
   * 验证URL格式
   */
  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证日期格式
   */
  isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * 验证年龄
   */
  isValidAge(birthDate: Date, minAge: number = 18, maxAge: number = 100): boolean {
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1 >= minAge && age - 1 <= maxAge;
    }
    
    return age >= minAge && age <= maxAge;
  }

  /**
   * 清理和验证输入文本
   */
  sanitizeText(text: string, maxLength: number = 1000): string {
    if (!text || typeof text !== 'string') {
      return '';
    }

    // 移除HTML标签和特殊字符
    const cleaned = text
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/[<>'"&]/g, '') // 移除危险字符
      .trim();

    return cleaned.length > maxLength ? cleaned.substring(0, maxLength) : cleaned;
  }

  /**
   * 验证文件类型
   */
  isValidFileType(filename: string, allowedTypes: string[]): boolean {
    const extension = filename.split('.').pop()?.toLowerCase();
    return extension ? allowedTypes.includes(extension) : false;
  }

  /**
   * 验证文件大小
   */
  isValidFileSize(fileSize: number, maxSize: number = 5 * 1024 * 1024): boolean {
    return fileSize > 0 && fileSize <= maxSize;
  }
}
