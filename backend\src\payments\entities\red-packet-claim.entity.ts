import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { RedPacket } from './red-packet.entity';

@Entity('red_packet_claims')
@Index(['redPacketId', 'userId'], { unique: true })
export class RedPacketClaim {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'red_packet_id' })
  redPacketId: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  amount: number;

  @Column({ name: 'claimed_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  claimedAt: Date;

  // 关联关系
  @ManyToOne(() => RedPacket, (redPacket) => redPacket.claims, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'red_packet_id' })
  redPacket: RedPacket;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
