import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  Length,
  Matches,
  IsOptional,
  IsObject,
} from 'class-validator';

export class RegisterDto {
  @ApiProperty({
    description: '手机号',
    example: '13800138000',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^1[3-9]\d{9}$/, { message: '手机号格式不正确' })
  phone: string;

  @ApiProperty({
    description: '密码',
    example: 'password123',
    minLength: 6,
    maxLength: 20,
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 20)
  @Matches(/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,20}$/, {
    message: '密码必须包含字母和数字，长度6-20位',
  })
  password: string;

  @ApiProperty({
    description: '短信验证码',
    example: '123456',
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6)
  @Matches(/^\d{6}$/, { message: '验证码必须是6位数字' })
  smsCode: string;

  @ApiProperty({
    description: '设备信息',
    required: false,
  })
  @IsOptional()
  @IsObject()
  deviceInfo?: {
    deviceId: string;
    deviceName?: string;
    deviceType?: string;
    osVersion?: string;
    appVersion?: string;
    pushToken?: string;
  };
}
