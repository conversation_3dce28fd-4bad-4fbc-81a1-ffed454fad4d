import { BaseMockRepository } from './base.repository.mock';
import { Transaction, TransactionFactory, TransactionType, TransactionStatus } from '../../fixtures/transactions/transaction.factory';

/**
 * 模拟交易仓库
 * 继承基础模拟仓库，实现交易特定方法
 */
export class TransactionMockRepository extends BaseMockRepository<Transaction> {
  /**
   * 查找钱包的所有交易
   * @param walletId 钱包ID
   * @returns 交易数组
   */
  findByWalletId(walletId: string): Promise<Transaction[]> {
    return this.findBy({ walletId } as Partial<Transaction>);
  }

  /**
   * 查找转账交易
   * @param walletId 源钱包ID
   * @param toWalletId 目标钱包ID
   * @returns 交易数组
   */
  findTransferTransactions(walletId: string, toWalletId: string): Promise<Transaction[]> {
    const transfers = this.entities.filter(tx => 
      tx.type === TransactionType.TRANSFER && 
      tx.walletId === walletId && 
      tx.toWalletId === toWalletId
    );

    return Promise.resolve(transfers);
  }

  /**
   * 查找特定类型的交易
   * @param walletId 钱包ID
   * @param type 交易类型
   * @returns 交易数组
   */
  findByType(walletId: string, type: TransactionType): Promise<Transaction[]> {
    return this.findBy({ 
      walletId, 
      type 
    } as Partial<Transaction>);
  }

  /**
   * 查找特定状态的交易
   * @param status 交易状态
   * @returns 交易数组
   */
  findByStatus(status: TransactionStatus): Promise<Transaction[]> {
    return this.findBy({ status } as Partial<Transaction>);
  }

  /**
   * 更新交易状态
   * @param transactionId 交易ID
   * @param status 新状态
   * @returns 更新后的交易
   */
  async updateStatus(transactionId: string, status: TransactionStatus): Promise<Transaction | null> {
    const transaction = await this.findOneById(transactionId);
    if (!transaction) return null;

    const updates: Partial<Transaction> = { 
      status, 
      updatedAt: new Date() 
    };

    // 如果交易完成，添加完成时间
    if (status === TransactionStatus.COMPLETED) {
      updates.completedAt = new Date();
    }

    return this.save({ ...transaction, ...updates });
  }

  /**
   * 初始化测试数据
   * @param walletIds 钱包ID数组
   * @param count 每个钱包的交易数量
   */
  seed(walletIds: string[], count = 3): void {
    const transactions: Transaction[] = [];

    walletIds.forEach(walletId => {
      // 创建存款交易
      transactions.push(
        ...TransactionFactory.createMany(count, { 
          walletId, 
          type: TransactionType.DEPOSIT 
        })
      );

      // 创建取款交易
      transactions.push(
        ...TransactionFactory.createMany(Math.max(1, count - 1), { 
          walletId, 
          type: TransactionType.WITHDRAWAL 
        })
      );

      // 为部分钱包创建转账交易
      if (walletIds.length > 1 && Math.random() > 0.3) {
        const otherWalletId = walletIds.find(id => id !== walletId);
        if (otherWalletId) {
          transactions.push(
            TransactionFactory.createTransfer(walletId, otherWalletId, 50)
          );
        }
      }
    });

    this.entities = transactions;
  }

  /**
   * 获取交易统计
   * @param walletId 钱包ID
   * @returns 交易统计信息
   */
  async getTransactionStats(walletId: string): Promise<{
    totalIncoming: number;
    totalOutgoing: number;
    count: number;
  }> {
    const transactions = await this.findByWalletId(walletId);

    let totalIncoming = 0;
    let totalOutgoing = 0;

    transactions.forEach(tx => {
      if (tx.status !== TransactionStatus.COMPLETED) return;

      if ([TransactionType.DEPOSIT, TransactionType.REFUND].includes(tx.type)) {
        totalIncoming += tx.amount;
      } else if ([TransactionType.WITHDRAWAL, TransactionType.PAYMENT].includes(tx.type)) {
        totalOutgoing += tx.amount;
      } else if (tx.type === TransactionType.TRANSFER) {
        if (tx.walletId === walletId) {
          totalOutgoing += tx.amount;
        } else if (tx.toWalletId === walletId) {
          totalIncoming += tx.amount;
        }
      }
    });

    return {
      totalIncoming,
      totalOutgoing,
      count: transactions.length
    };
  }

  /**
   * 获取最近交易
   * @param walletId 钱包ID
   * @param limit 限制数量
   * @returns 交易列表
   */
  async getRecentTransactions(walletId: string, limit = 10): Promise<Transaction[]> {
    const transactions = await this.findByWalletId(walletId);

    const sorted = [...transactions].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    return sorted.slice(0, limit);
  }
}

/**
 * 创建预填充的交易仓库
 * @param initialTransactions 初始交易数组
 * @returns 交易仓库实例
 */
export function createTransactionRepository(initialTransactions: Transaction[] = []): TransactionMockRepository {
  const repo = new TransactionMockRepository();

  if (initialTransactions.length > 0) {
    repo.initialize(initialTransactions);
  }

  return repo;
}
