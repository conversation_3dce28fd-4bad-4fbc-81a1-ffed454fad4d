import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Wallet } from '../../wallets/entities/wallet.entity';
import { TransactionLog } from './transaction-log.entity';

export enum TransactionType {
  TRANSFER = 'TRANSFER',
  DEPOSIT = 'DEPOSIT',
  WITHDRAWAL = 'WITHDRAWAL',
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  RED_PACKET = 'RED_PACKET',
  RED_PACKET_CLAIM = 'RED_PACKET_CLAIM',
}

export enum TransactionStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

@Entity('transactions')
@Index(['transactionNo'], { unique: true })
@Index(['fromUserId'])
@Index(['toUserId'])
@Index(['status'])
@Index(['type'])
@Index(['createdAt'])
export class Transaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'transaction_no', length: 32, unique: true })
  transactionNo: string;

  @Column({ name: 'from_user_id', nullable: true })
  fromUserId: string;

  @Column({ name: 'to_user_id', nullable: true })
  toUserId: string;

  @Column({ name: 'from_wallet_id', nullable: true })
  fromWalletId: string;

  @Column({ name: 'to_wallet_id', nullable: true })
  toWalletId: string;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  amount: number;

  @Column({
    type: 'decimal',
    precision: 15,
    scale: 2,
    default: 0.00,
    transformer: {
      to: (value: number) => value,
      from: (value: string) => parseFloat(value),
    },
  })
  fee: number;

  @Column({ length: 10, default: 'CNY' })
  currency: string;

  @Column({
    type: 'enum',
    enum: TransactionType,
  })
  type: TransactionType;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  status: TransactionStatus;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'reference_id', nullable: true, length: 100 })
  referenceId: string;

  @Column({ name: 'payment_method', nullable: true, length: 50 })
  paymentMethod: string;

  @Column({ name: 'processed_at', type: 'timestamp', nullable: true })
  processedAt: Date;

  @Column({ name: 'failed_reason', nullable: true })
  failedReason: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => User, (user) => user.sentTransactions)
  @JoinColumn({ name: 'from_user_id' })
  fromUser: User;

  @ManyToOne(() => User, (user) => user.receivedTransactions)
  @JoinColumn({ name: 'to_user_id' })
  toUser: User;

  @ManyToOne(() => Wallet, (wallet) => wallet.outgoingTransactions)
  @JoinColumn({ name: 'from_wallet_id' })
  fromWallet: Wallet;

  @ManyToOne(() => Wallet, (wallet) => wallet.incomingTransactions)
  @JoinColumn({ name: 'to_wallet_id' })
  toWallet: Wallet;

  @OneToMany(() => TransactionLog, (log) => log.transaction)
  logs: TransactionLog[];

  // 虚拟属性
  get totalAmount(): number {
    return this.amount + this.fee;
  }

  get isCompleted(): boolean {
    return this.status === TransactionStatus.COMPLETED;
  }

  get isFailed(): boolean {
    return this.status === TransactionStatus.FAILED;
  }

  get isPending(): boolean {
    return this.status === TransactionStatus.PENDING;
  }

  get isProcessing(): boolean {
    return this.status === TransactionStatus.PROCESSING;
  }

  get isCancelled(): boolean {
    return this.status === TransactionStatus.CANCELLED;
  }

  // 方法
  /**
   * 开始处理交易
   */
  startProcessing(): void {
    if (this.status !== TransactionStatus.PENDING) {
      throw new Error('只有待处理的交易才能开始处理');
    }
    this.status = TransactionStatus.PROCESSING;
  }

  /**
   * 完成交易
   */
  complete(): void {
    if (this.status !== TransactionStatus.PROCESSING) {
      throw new Error('只有处理中的交易才能完成');
    }
    this.status = TransactionStatus.COMPLETED;
    this.processedAt = new Date();
  }

  /**
   * 交易失败
   */
  fail(reason: string): void {
    if (this.status === TransactionStatus.COMPLETED) {
      throw new Error('已完成的交易不能设为失败');
    }
    this.status = TransactionStatus.FAILED;
    this.failedReason = reason;
    this.processedAt = new Date();
  }

  /**
   * 取消交易
   */
  cancel(reason?: string): void {
    if (this.status === TransactionStatus.COMPLETED) {
      throw new Error('已完成的交易不能取消');
    }
    if (this.status === TransactionStatus.PROCESSING) {
      throw new Error('处理中的交易不能取消');
    }
    this.status = TransactionStatus.CANCELLED;
    if (reason) {
      this.failedReason = reason;
    }
  }

  /**
   * 添加元数据
   */
  addMetadata(key: string, value: any): void {
    if (!this.metadata) {
      this.metadata = {};
    }
    this.metadata[key] = value;
  }

  /**
   * 获取元数据
   */
  getMetadata(key: string): any {
    return this.metadata?.[key];
  }

  /**
   * 检查是否可以退款
   */
  canRefund(): boolean {
    return (
      this.status === TransactionStatus.COMPLETED &&
      this.type !== TransactionType.REFUND &&
      this.type !== TransactionType.RED_PACKET_CLAIM
    );
  }

  /**
   * 获取交易摘要
   */
  getSummary(): {
    id: string;
    transactionNo: string;
    type: TransactionType;
    status: TransactionStatus;
    amount: number;
    fee: number;
    totalAmount: number;
    currency: string;
    description: string;
    createdAt: Date;
    processedAt: Date;
  } {
    return {
      id: this.id,
      transactionNo: this.transactionNo,
      type: this.type,
      status: this.status,
      amount: this.amount,
      fee: this.fee,
      totalAmount: this.totalAmount,
      currency: this.currency,
      description: this.description,
      createdAt: this.createdAt,
      processedAt: this.processedAt,
    };
  }
}
