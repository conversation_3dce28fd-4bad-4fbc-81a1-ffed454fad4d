# 钱包应用技术架构详细设计

## 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层 (Client Layer)                    │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Flutter App   │   Web App       │   Desktop App           │
│   (iOS/Android) │   (PWA)         │   (Windows/macOS)       │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
                    ┌───────┴───────┐
                    │   API Gateway │
                    │   (Kong/Nginx) │
                    └───────┬───────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                   微服务层 (Microservices)                   │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│ 用户服务     │ 支付服务     │ 资产服务     │ 通知服务         │
│ User Service│Payment Svc  │Asset Service│Notification Svc │
└─────────────┴─────────────┴─────────────┴─────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                   数据层 (Data Layer)                       │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│ PostgreSQL  │ Redis       │ MongoDB     │ Elasticsearch   │
│ (主数据库)   │ (缓存)       │ (日志)       │ (搜索)           │
└─────────────┴─────────────┴─────────────┴─────────────────┘
```

## 前端架构设计

### Flutter应用架构

采用Clean Architecture + MVVM模式：

```
lib/
├── core/                           # 核心层
│   ├── constants/                  # 常量定义
│   ├── errors/                     # 错误处理
│   ├── network/                    # 网络层
│   ├── storage/                    # 本地存储
│   ├── utils/                      # 工具类
│   └── theme/                      # 主题配置
├── data/                           # 数据层
│   ├── datasources/               # 数据源
│   │   ├── local/                 # 本地数据源
│   │   └── remote/                # 远程数据源
│   ├── models/                    # 数据模型
│   └── repositories/              # 仓库实现
├── domain/                        # 业务层
│   ├── entities/                  # 实体
│   ├── repositories/              # 仓库接口
│   └── usecases/                  # 用例
├── presentation/                  # 表现层
│   ├── pages/                     # 页面
│   ├── widgets/                   # 组件
│   ├── providers/                 # 状态管理
│   └── routes/                    # 路由配置
└── main.dart                      # 应用入口
```

### 状态管理方案

使用Riverpod进行状态管理：

```dart
// 用户状态管理
final userProvider = StateNotifierProvider<UserNotifier, UserState>((ref) {
  return UserNotifier(ref.read(userRepositoryProvider));
});

class UserNotifier extends StateNotifier<UserState> {
  final UserRepository _userRepository;
  
  UserNotifier(this._userRepository) : super(UserState.initial());
  
  Future<void> login(String phone, String password) async {
    state = state.copyWith(isLoading: true);
    try {
      final user = await _userRepository.login(phone, password);
      state = state.copyWith(user: user, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: e.toString(), isLoading: false);
    }
  }
}
```

### UI组件设计

#### 液态毛玻璃效果组件

```dart
class GlassmorphismCard extends StatelessWidget {
  final Widget child;
  final double blur;
  final double opacity;
  final BorderRadius borderRadius;

  const GlassmorphismCard({
    Key? key,
    required this.child,
    this.blur = 10.0,
    this.opacity = 0.2,
    this.borderRadius = const BorderRadius.all(Radius.circular(20)),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius,
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(opacity),
            borderRadius: borderRadius,
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: child,
        ),
      ),
    );
  }
}
```

#### iOS风格导航栏

```dart
class CustomCupertinoNavigationBar extends StatelessWidget 
    implements ObstructingPreferredSizeWidget {
  final String title;
  final Widget? leading;
  final List<Widget>? trailing;

  const CustomCupertinoNavigationBar({
    Key? key,
    required this.title,
    this.leading,
    this.trailing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CupertinoNavigationBar(
      backgroundColor: Colors.transparent,
      border: null,
      middle: Text(
        title,
        style: const TextStyle(
          fontSize: 17,
          fontWeight: FontWeight.w600,
        ),
      ),
      leading: leading,
      trailing: trailing != null 
        ? Row(
            mainAxisSize: MainAxisSize.min,
            children: trailing!,
          )
        : null,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(44.0);

  @override
  bool shouldFullyObstruct(BuildContext context) => false;
}
```

## 后端架构设计

### 微服务架构

#### 用户服务 (User Service)
```typescript
// 用户实体
@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  phone: string;

  @Column({ nullable: true })
  email: string;

  @Column()
  passwordHash: string;

  @Column({ default: false })
  isVerified: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

// 用户服务
@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private jwtService: JwtService,
    private cryptoService: CryptoService,
  ) {}

  async register(registerDto: RegisterDto): Promise<AuthResponse> {
    const { phone, password } = registerDto;
    
    // 检查用户是否已存在
    const existingUser = await this.userRepository.findOne({ 
      where: { phone } 
    });
    
    if (existingUser) {
      throw new ConflictException('用户已存在');
    }

    // 创建新用户
    const passwordHash = await this.cryptoService.hashPassword(password);
    const user = this.userRepository.create({
      phone,
      passwordHash,
    });

    await this.userRepository.save(user);

    // 生成JWT令牌
    const tokens = await this.generateTokens(user);
    
    return {
      user: this.sanitizeUser(user),
      ...tokens,
    };
  }

  private async generateTokens(user: User) {
    const payload = { sub: user.id, phone: user.phone };
    
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, { expiresIn: '15m' }),
      this.jwtService.signAsync(payload, { expiresIn: '7d' }),
    ]);

    return { accessToken, refreshToken };
  }
}
```

#### 支付服务 (Payment Service)
```typescript
@Entity('transactions')
export class Transaction {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  fromUserId: string;

  @Column()
  toUserId: string;

  @Column('decimal', { precision: 15, scale: 2 })
  amount: number;

  @Column()
  currency: string;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  status: TransactionStatus;

  @Column({
    type: 'enum',
    enum: TransactionType,
  })
  type: TransactionType;

  @CreateDateColumn()
  createdAt: Date;
}

@Injectable()
export class PaymentService {
  constructor(
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    private walletService: WalletService,
    private notificationService: NotificationService,
  ) {}

  async transfer(transferDto: TransferDto): Promise<Transaction> {
    const { fromUserId, toUserId, amount, currency } = transferDto;

    // 开始数据库事务
    return await this.transactionRepository.manager.transaction(
      async (transactionalEntityManager) => {
        // 检查余额
        const fromWallet = await this.walletService.getWallet(
          fromUserId, 
          currency
        );
        
        if (fromWallet.balance < amount) {
          throw new BadRequestException('余额不足');
        }

        // 创建交易记录
        const transaction = transactionalEntityManager.create(Transaction, {
          fromUserId,
          toUserId,
          amount,
          currency,
          type: TransactionType.TRANSFER,
          status: TransactionStatus.PENDING,
        });

        await transactionalEntityManager.save(transaction);

        try {
          // 扣减发送方余额
          await this.walletService.deductBalance(
            fromUserId, 
            amount, 
            currency,
            transactionalEntityManager
          );

          // 增加接收方余额
          await this.walletService.addBalance(
            toUserId, 
            amount, 
            currency,
            transactionalEntityManager
          );

          // 更新交易状态
          transaction.status = TransactionStatus.COMPLETED;
          await transactionalEntityManager.save(transaction);

          // 发送通知
          await this.notificationService.sendTransferNotification(
            transaction
          );

          return transaction;
        } catch (error) {
          transaction.status = TransactionStatus.FAILED;
          await transactionalEntityManager.save(transaction);
          throw error;
        }
      }
    );
  }
}
```

### 数据库设计

#### 核心表结构

```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    phone VARCHAR(20) UNIQUE NOT NULL,
    email VARCHAR(255),
    password_hash VARCHAR(255) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    kyc_status VARCHAR(20) DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 钱包表
CREATE TABLE wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    currency VARCHAR(10) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    frozen_balance DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, currency)
);

-- 交易表
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_user_id UUID REFERENCES users(id),
    to_user_id UUID REFERENCES users(id),
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    type VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 银行卡表
CREATE TABLE bank_cards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    card_number_encrypted TEXT NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    card_type VARCHAR(20) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 安全架构

#### JWT认证中间件
```typescript
@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    
    if (!token) {
      throw new UnauthorizedException('访问令牌缺失');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token);
      request.user = payload;
      return true;
    } catch (error) {
      throw new UnauthorizedException('访问令牌无效');
    }
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
```

#### 数据加密服务
```typescript
@Injectable()
export class CryptoService {
  private readonly algorithm = 'aes-256-gcm';
  private readonly secretKey = process.env.ENCRYPTION_KEY;

  async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  }

  async comparePassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash);
  }

  encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.secretKey);
    cipher.setAAD(Buffer.from('wallet-app'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
  }

  decrypt(encryptedData: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedData.split(':');
    
    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');
    
    const decipher = crypto.createDecipher(this.algorithm, this.secretKey);
    decipher.setAAD(Buffer.from('wallet-app'));
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

## 部署架构

### Docker容器化

```dockerfile
# Dockerfile for Backend
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

RUN npm run build

EXPOSE 3000

CMD ["npm", "run", "start:prod"]
```

### Kubernetes部署配置

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wallet-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wallet-backend
  template:
    metadata:
      labels:
        app: wallet-backend
    spec:
      containers:
      - name: wallet-backend
        image: wallet-app/backend:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: wallet-secrets
              key: database-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: wallet-secrets
              key: jwt-secret
```

这个架构设计提供了：
1. 可扩展的微服务架构
2. 安全的数据处理和存储
3. 现代化的前端用户体验
4. 完整的CI/CD流程
5. 高可用性和容错能力
