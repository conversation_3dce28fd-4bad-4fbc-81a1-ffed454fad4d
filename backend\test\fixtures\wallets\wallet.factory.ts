import { v4 as uuid } from 'uuid';

/**
 * 钱包类型枚举
 */
export enum WalletType {
  PRIMARY = 'primary',
  SAVINGS = 'savings',
  INVESTMENT = 'investment',
  BUSINESS = 'business'
}

/**
 * 钱包状态枚举
 */
export enum WalletStatus {
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CLOSED = 'closed'
}

/**
 * 钱包类型接口
 */
export interface Wallet {
  id: string;
  userId: string;
  name: string;
  type: WalletType;
  balance: number;
  currency: string;
  status: WalletStatus;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 钱包数据工厂
 * 用于创建用于测试的钱包数据
 */
export class WalletFactory {
  /**
   * 创建钱包对象
   * @param override 要覆盖的属性
   * @returns 钱包对象
   */
  static create(override: Partial<Wallet> = {}): Wallet {
    const now = new Date();

    return {
      id: uuid(),
      userId: uuid(),
      name: '主钱包',
      type: WalletType.PRIMARY,
      balance: 1000.00,
      currency: 'CNY',
      status: WalletStatus.ACTIVE,
      createdAt: now,
      updatedAt: now,
      ...override
    };
  }

  /**
   * 创建多个钱包对象
   * @param count 要创建的钱包数量
   * @param override 要覆盖的属性
   * @returns 钱包对象数组
   */
  static createMany(count: number, override: Partial<Wallet> = {}): Wallet[] {
    return Array.from({ length: count }, () => this.create(override));
  }

  /**
   * 创建用户的多个钱包
   * @param userId 用户ID
   * @param count 钱包数量
   * @returns 钱包对象数组
   */
  static createForUser(userId: string, count = 1): Wallet[] {
    return this.createMany(count, { userId });
  }

  /**
   * 创建特定类型的钱包
   * @param type 钱包类型
   * @param override 要覆盖的其他属性
   * @returns 钱包对象
   */
  static createWithType(type: WalletType, override: Partial<Wallet> = {}): Wallet {
    let name = '主钱包';
    switch (type) {
      case WalletType.SAVINGS:
        name = '储蓄钱包';
        break;
      case WalletType.INVESTMENT:
        name = '投资钱包';
        break;
      case WalletType.BUSINESS:
        name = '商务钱包';
        break;
    }

    return this.create({ type, name, ...override });
  }
}
