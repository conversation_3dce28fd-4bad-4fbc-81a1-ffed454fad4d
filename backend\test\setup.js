// 测试设置文件
// 在所有测试执行前运行

// 设置测试超时时间
jest.setTimeout(30000);

// 配置全局变量
global.__TEST__ = true;

// 配置测试环境变量
process.env.NODE_ENV = 'test';
process.env.DATABASE_HOST = 'localhost';
process.env.DATABASE_PORT = '5432';
process.env.DATABASE_USERNAME = 'test';
process.env.DATABASE_PASSWORD = 'test';
process.env.DATABASE_NAME = 'wallet_test';
process.env.JWT_SECRET = 'test_jwt_secret_key';
process.env.JWT_EXPIRES = '1d';

// 监听未捕获的Promise异常
process.on('unhandledRejection', (reason) => {
  console.error('Unhandled Promise Rejection in tests:', reason);
});

// 添加自定义匹配器
expect.extend({
  toBeWithinRange(received, floor, ceiling) {
    const pass = received >= floor && received <= ceiling;
    if (pass) {
      return {
        message: () => `expected ${received} not to be within range ${floor} - ${ceiling}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within range ${floor} - ${ceiling}`,
        pass: false,
      };
    }
  },

  toMatchTimestamp(received, expectedTimestamp, toleranceMs = 1000) {
    const receivedTime = new Date(received).getTime();
    const expectedTime = new Date(expectedTimestamp).getTime();
    const difference = Math.abs(receivedTime - expectedTime);
    const pass = difference <= toleranceMs;

    if (pass) {
      return {
        message: () => `expected ${received} not to be within ${toleranceMs}ms of ${expectedTimestamp}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be within ${toleranceMs}ms of ${expectedTimestamp}`,
        pass: false,
      };
    }
  },
});

// 全局控制台消息处理
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// 重写console.error，在测试环境中减少不必要的错误输出
console.error = (...args) => {
  // 在这里可以过滤不想看到的错误消息
  // 例如忽略特定的数据库连接错误
  const errorMessage = args.join(' ');
  if (errorMessage.includes('known test error - safe to ignore')) {
    return;
  }
  originalConsoleError(...args);
};

// 在测试结束后恢复控制台函数
afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});
