import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  Matches,
  IsOptional,
  IsObject,
} from 'class-validator';

export class LoginDto {
  @ApiProperty({
    description: '手机号',
    example: '13800138000',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^1[3-9]\d{9}$/, { message: '手机号格式不正确' })
  phone: string;

  @ApiProperty({
    description: '密码',
    example: 'password123',
  })
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiProperty({
    description: '设备信息',
    required: false,
  })
  @IsOptional()
  @IsObject()
  deviceInfo?: {
    deviceId: string;
    deviceName?: string;
    deviceType?: string;
    osVersion?: string;
    appVersion?: string;
    pushToken?: string;
  };
}
